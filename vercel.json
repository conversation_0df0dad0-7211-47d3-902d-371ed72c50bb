{"name": "flow-balance", "version": 2, "framework": "nextjs", "buildCommand": "chmod +x scripts/vercel-build.sh && ./scripts/vercel-build.sh", "devCommand": "pnpm dev", "installCommand": "pnpm install", "outputDirectory": ".next", "public": false, "functions": {"src/app/api/**/*.ts": {"maxDuration": 60, "memory": 1024}, "src/app/**/page.tsx": {"maxDuration": 30, "memory": 512}}, "env": {"NODE_ENV": "production", "NEXT_TELEMETRY_DISABLED": "1", "PRISMA_CLIENT_ENGINE_TYPE": "binary", "PRISMA_GENERATE_SKIP_AUTOINSTALL": "true"}, "build": {"env": {"NODE_ENV": "production", "NEXT_TELEMETRY_DISABLED": "1", "PRISMA_CLIENT_ENGINE_TYPE": "binary", "PRISMA_GENERATE_SKIP_AUTOINSTALL": "true"}}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}, {"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "SAMEORIGIN"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}], "redirects": [{"source": "/health", "destination": "/api/health", "permanent": false}]}