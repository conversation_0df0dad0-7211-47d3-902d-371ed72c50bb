# Flow Balance - SQLite Docker Compose
# SQLite 数据库的简化配置

version: '3.8'

services:
  # 应用服务
  flow-balance:
    image: ghcr.io/jomonylw/flow-balance:latest
    container_name: flow-balance
    ports:
      - '3000:3000'
    environment:
      - NODE_ENV=production
      - DATABASE_URL=file:/app/data/flow-balance.db
      - NEXT_PUBLIC_APP_URL=${NEXT_PUBLIC_APP_URL:-http://localhost:3000}
      - NEXT_TELEMETRY_DISABLED=1
      # JWT_SECRET 会自动生成，无需手动配置
    volumes:
      # 持久化数据库文件
      - ./data:/app/data
      # 可选：持久化日志
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ['CMD', 'node', 'healthcheck.js']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  # Nginx 反向代理（可选，用于生产环境）
  nginx:
    image: nginx:alpine
    container_name: flow-balance-nginx
    ports:
      - '80:80'
      - '443:443'
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - flow-balance
    restart: unless-stopped
    healthcheck:
      test:
        [
          'CMD',
          'wget',
          '--quiet',
          '--tries=1',
          '--spider',
          'http://localhost/health',
        ]
      interval: 30s
      timeout: 10s
      retries: 3
    profiles:
      - production # 只在生产环境启用

networks:
  default:
    name: flow-balance-network
