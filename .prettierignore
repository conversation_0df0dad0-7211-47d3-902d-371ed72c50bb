# Dependencies
node_modules/
.pnpm-store/

# Build outputs
.next/
out/
dist/
build/

# Database
*.db
*.db-journal

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Generated files
*.tsbuildinfo
.eslintcache

# Coverage
coverage/
.nyc_output/

# Prisma
prisma/migrations/
prisma/dev.db*

# Documentation
docs/generated/
