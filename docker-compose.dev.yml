# Flow Balance - Development Docker Compose
# 开发环境 Docker 配置，支持热重载和调试

version: '3.8'

services:
  # 应用服务 - 开发模式
  app:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - '3000:3000'
    environment:
      - NODE_ENV=development
      - DATABASE_URL=file:./data/dev.db
      - JWT_SECRET=dev-jwt-secret-change-in-production
      - NEXT_PUBLIC_APP_URL=http://localhost:3000
      - NEXT_TELEMETRY_DISABLED=1
    volumes:
      # 挂载源代码以支持热重载
      - .:/app
      - /app/node_modules
      - /app/.next
      # 持久化数据库文件
      - dev_data:/app/data
    depends_on:
      - postgres
    networks:
      - flow-balance-dev
    restart: unless-stopped
    command: pnpm dev

  # PostgreSQL 数据库服务（可选）
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: flowbalance_dev
      POSTGRES_USER: flowbalance
      POSTGRES_PASSWORD: dev_password_change_in_production
    ports:
      - '5432:5432'
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - flow-balance-dev
    restart: unless-stopped
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U flowbalance -d flowbalance_dev']
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis 缓存服务（可选，用于会话存储）
  redis:
    image: redis:7-alpine
    ports:
      - '6379:6379'
    volumes:
      - redis_dev_data:/data
    networks:
      - flow-balance-dev
    restart: unless-stopped
    command: redis-server --appendonly yes
    healthcheck:
      test: ['CMD', 'redis-cli', 'ping']
      interval: 10s
      timeout: 3s
      retries: 3

  # Prisma Studio（数据库管理界面）
  prisma-studio:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - '5555:5555'
    environment:
      - DATABASE_URL=file:./data/dev.db
    volumes:
      - .:/app
      - dev_data:/app/data
    networks:
      - flow-balance-dev
    command: pnpm db:studio
    depends_on:
      - app

volumes:
  dev_data:
    driver: local
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local

networks:
  flow-balance-dev:
    driver: bridge
