# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

/src/generated/prisma

# Database files
*.db
*.db-journal
*.db-shm
*.db-wal
dev.db
test.db
production.db
prisma/dev.db
prisma/test.db
prisma/production.db
prisma/dev_bk.db
prisma/prisma/dev.db

# TypeScript build info
*.tsbuildinfo
tsconfig.tsbuildinfo
tsconfig.strict.tsbuildinfo

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~
.project
.classpath
.settings/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov
.nyc_output/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Temporary folders
tmp/
temp/

# Backup files
*.backup
*.bak
*.orig

# Docker volumes and data
docker-data/
postgres-data/
redis-data/

# Monitoring and logs
monitoring-report-*.txt
backups/

# Test files and temporary data
test-*.md
todo.md
*.test.local

# Local configuration files
.env.local.backup
.env.production.backup

# Build artifacts
dist/
build/

# Prisma generated files (keep schema but ignore generated client in some cases)
# Note: We keep prisma/schema.prisma and migrations

# Application specific
refactor-progress.json

# ===========================================
# 选择性忽略杂乱的开发文档和脚本
# ===========================================

# docs/ 文件夹中的杂乱文件
docs/test-*.md
docs/debug-*.md
docs/*_FIX_SUMMARY.md
docs/*_FIXES_SUMMARY.md
docs/*_IMPLEMENTATION_SUMMARY.md
docs/*_COMPLETION_SUMMARY.md
docs/todo.md
docs/review_results.md
docs/optimization-summary.md
docs/verify-*.md
docs/千位符格式化完成报告.md

# scripts/ 文件夹中的杂乱文件
scripts/test-*.ts
scripts/test-*.js
scripts/debug-*.ts
scripts/debug-*.js
scripts/fix-*.ts
scripts/fix-*.js
scripts/verify-*.ts
scripts/verify-*.js
scripts/check-*.ts
scripts/check-*.js
scripts/migrate-*.js
scripts/migrate-*.ts
scripts/refactor-*.js
scripts/refactor-*.ts
scripts/batch-*.js
scripts/cleanup-*.sh
scripts/cleanup-*.js
scripts/analyze-*.js
scripts/track-*.js
scripts/update-*.js
scripts/update-*.ts
scripts/create-*.ts
scripts/add-*.ts
scripts/add-*.js
scripts/force-*.ts
scripts/final-*.ts
scripts/smart-*.js
scripts/targeted-*.js
scripts/simple-*.js
scripts/run-*.ts
scripts/refactor-success-report.md

dev-files-backup-*
prisma/prisma/dev.db

# 如果您希望完全忽略这些文件夹，取消下面的注释
# /docs
# /CODE_GUIDE_DOC
# /scripts
