# Flow Balance - Production Docker Compose
# 生产环境 Docker 配置

version: '3.8'

services:
  # 应用服务
  app:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        BUILD_DATE: ${BUILD_DATE}
        GIT_COMMIT: ${GIT_COMMIT}
    ports:
      - '3000:3000'
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL:-file:./data/production.db}
      - JWT_SECRET=${JWT_SECRET}
      - NEXT_PUBLIC_APP_URL=${NEXT_PUBLIC_APP_URL:-http://localhost:3000}
      - NEXT_TELEMETRY_DISABLED=1
    volumes:
      # 持久化数据库文件（仅当使用 SQLite 时）
      - app_data:/app/data
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - flow-balance
    restart: unless-stopped
    healthcheck:
      test: ['CMD', 'node', 'healthcheck.js']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # PostgreSQL 数据库服务
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-flowbalance}
      POSTGRES_USER: ${POSTGRES_USER:-flowbalance}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    ports:
      - '5432:5432'
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - flow-balance
    restart: unless-stopped
    healthcheck:
      test:
        [
          'CMD-SHELL',
          'pg_isready -U ${POSTGRES_USER:-flowbalance} -d ${POSTGRES_DB:-flowbalance}',
        ]
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

  # Nginx 反向代理（可选）
  nginx:
    image: nginx:alpine
    ports:
      - '80:80'
      - '443:443'
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - app
    networks:
      - flow-balance
    restart: unless-stopped
    healthcheck:
      test:
        [
          'CMD',
          'wget',
          '--quiet',
          '--tries=1',
          '--spider',
          'http://localhost/health',
        ]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis 缓存服务（可选）
  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    networks:
      - flow-balance
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-}
    healthcheck:
      test: ['CMD', 'redis-cli', 'ping']
      interval: 10s
      timeout: 3s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 128M
        reservations:
          memory: 64M

volumes:
  app_data:
    driver: local
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  flow-balance:
    driver: bridge
