{"name": "persional-balance-sheet", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "prisma generate && next build", "start": "next start", "postinstall": "prisma generate", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "type-check:detailed": "node scripts/type-check.js", "type-check:strict": "tsc --noEmit --project tsconfig.strict.json", "analyze-types": "node scripts/analyze-type-usage.js", "refactor-types": "node scripts/refactor-types.js", "track-progress": "node scripts/track-refactor-progress.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "db:migrate": "prisma migrate dev", "db:generate": "prisma generate", "db:seed": "node prisma/seed.js", "db:reset": "prisma migrate reset --force", "db:studio": "prisma studio", "db:deploy": "prisma migrate deploy", "analyze": "ANALYZE=true npm run build", "clean": "rm -rf .next out dist build", "format": "prettier --write .", "format:check": "prettier --check .", "prepare": "husky install", "pre-commit": "lint-staged", "docker:build": "docker build -t flow-balance .", "docker:run": "docker run -p 3000:3000 flow-balance", "docker:dev": "docker-compose -f docker-compose.dev.yml up", "docker:prod": "docker-compose up", "docker:down": "docker-compose down", "quick-start": "./scripts/quick-start.sh", "switch-db": "node scripts/switch-database.js", "health": "curl -f http://localhost:3000/api/health", "backup": "node scripts/backup-data.js", "deploy:vercel": "vercel --prod", "deploy:docker": "docker-compose up -d --build", "version:show": "./scripts/version-manager.sh current", "version:bump:patch": "./scripts/version-manager.sh bump patch", "version:bump:minor": "./scripts/version-manager.sh bump minor", "version:bump:major": "./scripts/version-manager.sh bump major", "version:tag": "./scripts/version-manager.sh tag", "version:docker-tags": "./scripts/version-manager.sh docker-tags", "release:patch": "./scripts/version-manager.sh release patch", "release:minor": "./scripts/version-manager.sh release minor", "release:major": "./scripts/version-manager.sh release major"}, "prisma": {"seed": "node prisma/seed.js"}, "dependencies": {"@heroicons/react": "^2.2.0", "@prisma/client": "^6.9.0", "bcryptjs": "^3.0.2", "date-fns": "^4.1.0", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.513.0", "next": "15.3.3", "prisma": "^6.9.0", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^14.6.1", "@types/bcryptjs": "^3.0.0", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "eslint-plugin-import": "^2.31.0", "husky": "^8.0.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.5.2", "prettier": "^3.5.3", "sharp": "^0.34.3", "tailwindcss": "^4", "tsx": "^4.19.4", "typescript": "^5", "webpack-bundle-analyzer": "^4.10.2", "zod": "^3.25.67"}}