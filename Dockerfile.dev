# Flow Balance - Development Dockerfile
# 开发环境 Docker 配置，支持热重载

FROM node:18-alpine

# 安装必要的系统包
RUN apk add --no-cache libc6-compat dumb-init

# 设置工作目录
WORKDIR /app

# 安装 pnpm
RUN npm install -g pnpm

# 复制 package 文件
COPY package.json pnpm-lock.yaml ./

# 安装依赖
RUN pnpm install

# 复制 Prisma schema
COPY prisma ./prisma/

# 生成 Prisma 客户端
RUN pnpm db:generate

# 创建数据目录
RUN mkdir -p /app/data

# 设置环境变量
ENV NODE_ENV=development
ENV NEXT_TELEMETRY_DISABLED=1

# 暴露端口
EXPOSE 3000
EXPOSE 5555

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:3000/api/health || exit 1

# 启动命令（将在 docker-compose 中覆盖）
CMD ["dumb-init", "pnpm", "dev"]
