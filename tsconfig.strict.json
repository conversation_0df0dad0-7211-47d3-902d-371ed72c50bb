{"extends": "./tsconfig.json", "compilerOptions": {"target": "ES2022", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "noUncheckedIndexedAccess": true, "noImplicitOverride": true, "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/lib/*": ["./src/lib/*"], "@/types/*": ["./src/types/*"], "@/hooks/*": ["./src/hooks/*"], "@/contexts/*": ["./src/contexts/*"], "@/utils/*": ["./src/lib/utils/*"], "@/services/*": ["./src/lib/services/*"], "@/constants/*": ["./src/lib/constants/*"], "@/api/*": ["./src/lib/api/*"], "@/database/*": ["./src/lib/database/*"], "@/config/*": ["./src/config/*"], "@/ui/*": ["./src/components/ui/*"], "@/features/*": ["./src/components/features/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "src/types/**/*.d.ts"], "exclude": ["node_modules", ".next", "out", "dist", "build"]}