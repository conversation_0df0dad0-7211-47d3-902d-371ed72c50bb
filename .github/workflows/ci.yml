# Flow Balance - Continuous Integration Workflow
# 持续集成工作流

name: CI

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

permissions:
  contents: read
  security-events: write
  actions: read

env:
  NODE_ENV: test
  DATABASE_URL: 'file:./dev.db'
  JWT_SECRET: 'test-jwt-secret-for-ci'
  NEXTAUTH_SECRET: 'test-nextauth-secret-for-ci'
  NEXTAUTH_URL: 'http://localhost:3000'

jobs:
  # 代码质量检查
  lint-and-test:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [18.x, 20.x]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}

      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9

      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Setup pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Create .env file
        run: |
          echo "NODE_ENV=test" > .env
          echo "DATABASE_URL=file:./dev.db" >> .env
          echo "JWT_SECRET=test-jwt-secret-for-ci" >> .env
          echo "NEXTAUTH_SECRET=test-nextauth-secret-for-ci" >> .env
          echo "NEXTAUTH_URL=http://localhost:3000" >> .env

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Generate Prisma client
        run: pnpm db:generate

      - name: Run ESLint
        run: pnpm lint

      - name: Run Prettier check
        run: pnpm format:check

      - name: Run TypeScript type check
        run: pnpm type-check

      - name: Run tests
        run: pnpm test:ci

      - name: Upload coverage reports
        uses: codecov/codecov-action@v3
        if: matrix.node-version == '18.x'
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella

  # 构建检查
  build-check:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9

      - name: Create .env file
        run: |
          echo "NODE_ENV=test" > .env
          echo "DATABASE_URL=file:./dev.db" >> .env
          echo "JWT_SECRET=test-jwt-secret-for-ci" >> .env
          echo "NEXTAUTH_SECRET=test-nextauth-secret-for-ci" >> .env
          echo "NEXTAUTH_URL=http://localhost:3000" >> .env

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Generate Prisma client
        run: pnpm db:generate

      - name: Build application
        run: pnpm build

      - name: Check build output
        run: |
          if [ ! -d ".next" ]; then
            echo "Build failed: .next directory not found"
            exit 1
          fi
          echo "Build successful"

  # 数据库迁移检查
  database-check:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9

      - name: Create .env file
        run: |
          echo "NODE_ENV=test" > .env
          echo "DATABASE_URL=file:./dev.db" >> .env
          echo "JWT_SECRET=test-jwt-secret-for-ci" >> .env
          echo "NEXTAUTH_SECRET=test-nextauth-secret-for-ci" >> .env
          echo "NEXTAUTH_URL=http://localhost:3000" >> .env

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Test SQLite migrations
        env:
          DATABASE_URL: 'file:./test.db'
        run: |
          pnpm db:generate
          pnpm db:deploy

  # 安全检查
  security-check:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9

      - name: Create .env file
        run: |
          echo "NODE_ENV=test" > .env
          echo "DATABASE_URL=file:./dev.db" >> .env
          echo "JWT_SECRET=test-jwt-secret-for-ci" >> .env
          echo "NEXTAUTH_SECRET=test-nextauth-secret-for-ci" >> .env
          echo "NEXTAUTH_URL=http://localhost:3000" >> .env

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run security audit
        run: pnpm audit --audit-level moderate

      - name: Run CodeQL Analysis
        uses: github/codeql-action/init@v3
        with:
          languages: javascript

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3
