# Flow Balance - Docker Build and Release Workflow
# 自动构建和发布 Docker 镜像

name: Docker Build and Release

on:
  push:
    branches:
      - main
      - develop
    tags:
      - 'v*'
  pull_request:
    branches:
      - main
      - develop

permissions:
  contents: read
  packages: write
  security-events: write
  actions: read

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}
  NODE_ENV: test
  DATABASE_URL: 'file:./dev.db'
  JWT_SECRET: 'test-jwt-secret-for-ci'
  NEXTAUTH_SECRET: 'test-nextauth-secret-for-ci'
  NEXTAUTH_URL: 'http://localhost:3000'

jobs:
  # 代码质量检查
  quality-check:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9

      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Setup pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Create .env file
        run: |
          echo "NODE_ENV=test" > .env
          echo "DATABASE_URL=file:./dev.db" >> .env
          echo "JWT_SECRET=test-jwt-secret-for-ci" >> .env
          echo "NEXTAUTH_SECRET=test-nextauth-secret-for-ci" >> .env
          echo "NEXTAUTH_URL=http://localhost:3000" >> .env

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Generate Prisma client
        run: pnpm db:generate

      - name: Run linting
        run: pnpm lint

      - name: Run type checking
        run: pnpm type-check

      - name: Run tests
        run: pnpm test:ci

  # 构建和推送 Docker 镜像
  docker-build:
    needs: quality-check
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          platforms: linux/amd64,linux/arm64
          driver-opts: |
            image=moby/buildkit:buildx-stable-1

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Get version from package.json
        id: package-version
        run: |
          VERSION=$(node -p "require('./package.json').version")
          echo "version=$VERSION" >> $GITHUB_OUTPUT
          echo "📦 Package version: $VERSION"

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=semver,pattern={{major}}
            type=raw,value=latest,enable={{is_default_branch}}
            type=raw,value=${{ steps.package-version.outputs.version }},enable={{is_default_branch}}
            type=raw,value=v${{ steps.package-version.outputs.version }},enable={{is_default_branch}}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          provenance: false
          sbom: false
          build-args: |
            BUILD_DATE=${{ fromJSON(steps.meta.outputs.json).labels['org.opencontainers.image.created'] }}
            GIT_COMMIT=${{ github.sha }}
            APP_VERSION=${{ steps.package-version.outputs.version }}

  # 安全扫描
  security-scan:
    needs: docker-build
    runs-on: ubuntu-latest
    if: github.event_name != 'pull_request'
    permissions:
      actions: read
      contents: read
      security-events: write

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:latest
          format: 'sarif'
          output: 'trivy-results.sarif'
          scanners: 'vuln'
          skip-files: '/root/.npm/_cacache/**'
          timeout: '10m'

      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

  # 发布 Release
  release:
    needs: [quality-check, docker-build]
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/v')

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Create Release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ github.ref }}
          release_name: Release ${{ github.ref }}
          body: |
            ## 🚀 Flow Balance Release ${{ github.ref }}

            ### 📦 Docker Images

            ```bash
            # 拉取最新镜像
            docker pull ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.ref_name }}

            # 运行容器
            docker run -p 3000:3000 ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.ref_name }}
            ```

            ### 🔧 部署说明

            1. **Docker Compose 部署**:
               ```bash
               wget https://raw.githubusercontent.com/${{ github.repository }}/${{ github.ref_name }}/docker-compose.yml
               docker-compose up -d
               ```

            2. **Vercel 部署**: 
               - 连接 GitHub 仓库
               - 设置环境变量
               - 自动部署

            ### 📋 更新内容

            请查看 [Commits](${{ github.event.compare }}) 了解详细更新内容。

            ### 🐛 问题反馈

            如有问题，请在 [Issues](https://github.com/${{ github.repository }}/issues) 中反馈。
          draft: false
          prerelease: false
