@import 'tailwindcss';

/* Tailwind v4 dark mode configuration */
@custom-variant dark (&:where(.dark, .dark *));

/* 基础主题变量 */
:root {
  --color-background: #ffffff;
  --color-foreground: #171717;
  --color-card: #ffffff;
  --color-card-foreground: #171717;
  --color-popover: #ffffff;
  --color-popover-foreground: #171717;
  --color-primary: #2563eb;
  --color-primary-foreground: #ffffff;
  --color-secondary: #f1f5f9;
  --color-secondary-foreground: #0f172a;
  --color-muted: #f8fafc;
  --color-muted-foreground: #64748b;
  --color-accent: #f1f5f9;
  --color-accent-foreground: #0f172a;
  --color-destructive: #dc2626;
  --color-destructive-foreground: #ffffff;
  --color-border: #e2e8f0;
  --color-input: #e2e8f0;
  --color-ring: #2563eb;
  --radius: 0.5rem;
}

/* 明亮主题 */
.light {
  --color-background: #ffffff !important;
  --color-foreground: #171717 !important;
  --color-card: #ffffff !important;
  --color-card-foreground: #171717 !important;
  --color-popover: #ffffff !important;
  --color-popover-foreground: #171717 !important;
  --color-primary: #2563eb !important;
  --color-primary-foreground: #ffffff !important;
  --color-secondary: #f1f5f9 !important;
  --color-secondary-foreground: #0f172a !important;
  --color-muted: #f8fafc !important;
  --color-muted-foreground: #64748b !important;
  --color-accent: #f1f5f9 !important;
  --color-accent-foreground: #0f172a !important;
  --color-destructive: #dc2626 !important;
  --color-destructive-foreground: #ffffff !important;
  --color-border: #e2e8f0 !important;
  --color-input: #e2e8f0 !important;
  --color-ring: #2563eb !important;
}

/* 深色主题 */
.dark {
  --color-background: #0a0a0a !important;
  --color-foreground: #ededed !important;
  --color-card: #1a1a1a !important;
  --color-card-foreground: #ededed !important;
  --color-popover: #1a1a1a !important;
  --color-popover-foreground: #ededed !important;
  --color-primary: #3b82f6 !important;
  --color-primary-foreground: #ffffff !important;
  --color-secondary: #1e293b !important;
  --color-secondary-foreground: #f8fafc !important;
  --color-muted: #1e293b !important;
  --color-muted-foreground: #94a3b8 !important;
  --color-accent: #1e293b !important;
  --color-accent-foreground: #f8fafc !important;
  --color-destructive: #ef4444 !important;
  --color-destructive-foreground: #ffffff !important;
  --color-border: #334155 !important;
  --color-input: #334155 !important;
  --color-ring: #3b82f6 !important;
}

/* Loading Spinner 自定义动画 */
@keyframes loading-bars {
  0%,
  40%,
  100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}

@keyframes loading-pulse {
  0%,
  100% {
    opacity: 0.4;
  }
  50% {
    opacity: 1;
  }
}

body {
  background-color: var(--color-background) !important;
  color: var(--color-foreground) !important;
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu',
    'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  /* 优化移动端滚动 */
  -webkit-overflow-scrolling: touch;
  /* 防止移动端缩放 */
  touch-action: manipulation;
  /* 强制过渡效果 */
  transition:
    background-color 0.2s ease,
    color 0.2s ease;
  /* 优化渲染性能，减少重绘 */
  will-change: auto;
  /* 启用硬件加速 */
  transform: translateZ(0);
}

/* 确保html元素也应用主题 */
html {
  background-color: var(--color-background) !important;
  color: var(--color-foreground) !important;
  transition:
    background-color 0.2s ease,
    color 0.2s ease;
}

/* 移动端优化 */
@media (max-width: 768px) {
  /* 优化触摸目标大小 */
  button,
  a,
  input:not([type='range']),
  select,
  textarea {
    min-height: 30px;
    min-width: 30px;
  }

  /* 滑块组件在移动端的特殊处理 */
  input[type='range'] {
    min-height: auto;
    min-width: auto;
  }

  /* 优化表格在移动端的显示 */
  .table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  /* 优化模态框在移动端的显示 */
  .modal-mobile {
    margin: 0;
    max-height: 100vh;
    border-radius: 0;
  }

  /* 仪表板卡片在移动端的优化 */
  .dashboard-card {
    min-width: 0;
    word-break: break-word;
    overflow-wrap: break-word;
  }

  /* 金额数字在移动端的优化 */
  .dashboard-amount {
    font-size: 1.25rem !important; /* 强制使用较小字体 */
    line-height: 1.4;
    word-break: break-all;
    overflow-wrap: break-word;
    hyphens: auto;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  /* 移除hover效果，使用active状态 */
  .hover-touch:hover {
    background-color: initial;
  }

  .hover-touch:active {
    background-color: rgba(0, 0, 0, 0.1);
  }
}

/* 高DPI屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  /* 确保图标和图片在高DPI屏幕上清晰 */
  svg,
  img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* 路由过渡优化 */
.route-transition {
  transition:
    opacity 0.15s ease-in-out,
    transform 0.15s ease-in-out;
}

.route-transition-enter {
  opacity: 0.95;
  transform: translateY(2px);
}

.route-transition-enter-active {
  opacity: 1;
  transform: translateY(0);
}

/* 侧边栏优化 */
.sidebar-container {
  /* 启用硬件加速 */
  transform: translateZ(0);
  /* 优化滚动性能 */
  will-change: scroll-position;
  /* 减少重绘 */
  contain: layout style paint;
}

/* 主内容区域优化 */
.main-content {
  /* 启用硬件加速 */
  transform: translateZ(0);
  /* 优化渲染性能 */
  will-change: auto;
  /* 减少重绘 */
  contain: layout style;
}

/* 账户项目hover效果 - 使用账户颜色 */
.account-hover-item:hover {
  background: linear-gradient(
    to right,
    var(--hover-bg-from, rgb(249 250 251)),
    var(--hover-bg-to, rgb(243 244 246))
  );
}

@media (prefers-color-scheme: dark) {
  .account-hover-item:hover {
    background: linear-gradient(
      to right,
      var(--hover-bg-from, rgba(31, 41, 55, 0.5)),
      var(--hover-bg-to, rgba(55, 65, 81, 0.3))
    );
  }
}

/* 账户项目选中状态 - 使用账户颜色 */
.account-active-item {
  background: linear-gradient(
    to right,
    var(--active-bg-from, rgb(239 246 255)),
    var(--active-bg-to, rgb(219 234 254))
  );
  border-color: var(--active-border, rgb(147 197 253));
}

@media (prefers-color-scheme: dark) {
  .account-active-item {
    background: linear-gradient(
      to right,
      var(--active-bg-from, rgba(30, 58, 138, 0.3)),
      var(--active-bg-to, rgba(30, 64, 175, 0.2))
    );
    border-color: var(--active-border, rgba(59, 130, 246, 0.5));
  }
}

/* 账户项目选中状态文字颜色 */
.account-active-text {
  color: var(--active-text-color, rgb(29, 78, 216));
}

@media (prefers-color-scheme: dark) {
  .account-active-text {
    color: var(--active-text-color, rgb(147, 197, 253));
  }
}

/* 分类图标样式 */
.category-icon-container {
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(8px);
  transform-origin: center;
  will-change: transform, box-shadow;
}

/* 分类图标动画关键帧 */
@keyframes category-icon-pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes category-icon-glow {
  0%,
  100% {
    box-shadow:
      0 1px 3px rgba(0, 0, 0, 0.05),
      inset 0 1px 0 rgba(255, 255, 255, 0.6);
  }
  50% {
    box-shadow:
      0 4px 12px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);
  }
}

/* 分类图标普通状态 */
.category-icon-normal {
  background: linear-gradient(
    135deg,
    var(--icon-bg-from, rgba(248, 250, 252, 0.9)),
    var(--icon-bg-to, rgba(241, 245, 249, 0.95))
  );
  border-color: var(--icon-border, rgba(107, 114, 128, 0.15));
  box-shadow:
    0 1px 3px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
}

/* 分类图标悬停状态 */
.category-icon-hover {
  background: linear-gradient(
    135deg,
    var(--icon-hover-bg-from, rgba(241, 245, 249, 0.95)),
    var(--icon-hover-bg-to, rgba(226, 232, 240, 1))
  );
  border-color: var(--icon-border, rgba(107, 114, 128, 0.25));
  box-shadow:
    0 2px 6px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.7);
  transform: translateY(-0.5px);
}

/* 分类图标选中状态 */
.category-icon-active {
  background: linear-gradient(
    135deg,
    var(--icon-hover-bg-from, rgba(239, 246, 255, 0.95)),
    var(--icon-hover-bg-to, rgba(219, 234, 254, 1))
  );
  border-color: var(--icon-border, rgba(59, 130, 246, 0.4));
  box-shadow:
    0 2px 8px rgba(59, 130, 246, 0.15),
    0 0 0 1px var(--icon-border, rgba(59, 130, 246, 0.2)),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  animation: category-icon-glow 2s ease-in-out infinite;
}

/* 分类图标颜色 */
.category-icon-color {
  color: var(--icon-color, rgb(107, 114, 128));
}

.category-icon-color-hover {
  color: var(--icon-hover-color, rgb(87, 94, 108));
}

.category-icon-color-active {
  color: var(--icon-hover-color, rgb(29, 78, 216));
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .category-icon-normal {
    background: linear-gradient(
      135deg,
      var(--icon-bg-from-dark, rgba(55, 65, 81, 0.7)),
      var(--icon-bg-to-dark, rgba(75, 85, 99, 0.9))
    );
    border-color: var(--icon-border-dark, rgba(107, 114, 128, 0.25));
    box-shadow:
      0 1px 3px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .category-icon-hover {
    background: linear-gradient(
      135deg,
      var(--icon-hover-bg-from-dark, rgba(75, 85, 99, 0.9)),
      var(--icon-hover-bg-to-dark, rgba(107, 114, 128, 1))
    );
    border-color: var(--icon-border-dark, rgba(107, 114, 128, 0.4));
    box-shadow:
      0 2px 6px rgba(0, 0, 0, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.15);
    transform: translateY(-0.5px);
  }

  .category-icon-active {
    background: linear-gradient(
      135deg,
      var(--icon-hover-bg-from-dark, rgba(30, 58, 138, 0.5)),
      var(--icon-hover-bg-to-dark, rgba(30, 64, 175, 0.4))
    );
    border-color: var(--icon-border-dark, rgba(59, 130, 246, 0.6));
    box-shadow:
      0 2px 8px rgba(59, 130, 246, 0.25),
      0 0 0 1px var(--icon-border-dark, rgba(59, 130, 246, 0.4)),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  .category-icon-color {
    color: var(--icon-color-dark, rgb(156, 163, 175));
  }

  .category-icon-color-hover {
    color: var(--icon-hover-color-dark, rgb(209, 213, 219));
  }

  .category-icon-color-active {
    color: var(--icon-hover-color-dark, rgb(147, 197, 253));
  }
}

/* 隐藏滚动条工具类 */
.scrollbar-hide {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}

/* 树状菜单美化样式 */
.tree-item-gradient-active {
  background: linear-gradient(
    135deg,
    rgb(239 246 255) 0%,
    rgb(219 234 254) 50%,
    rgb(191 219 254) 100%
  );
}

.dark .tree-item-gradient-active {
  background: linear-gradient(
    135deg,
    rgb(30 58 138 / 0.3) 0%,
    rgb(30 64 175 / 0.2) 50%,
    rgb(37 99 235 / 0.1) 100%
  );
}

.tree-item-gradient-hover {
  background: linear-gradient(
    135deg,
    rgb(249 250 251) 0%,
    rgb(243 244 246) 50%,
    rgb(229 231 235) 100%
  );
}

.dark .tree-item-gradient-hover {
  background: linear-gradient(
    135deg,
    rgb(31 41 55 / 0.5) 0%,
    rgb(55 65 81 / 0.3) 50%,
    rgb(75 85 99 / 0.2) 100%
  );
}

/* 文本截断工具类 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--color-muted);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(100, 116, 139, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(100, 116, 139, 0.5);
}

/* 暗色主题下的滚动条 */
.dark ::-webkit-scrollbar-track {
  background: var(--color-muted);
}

.dark ::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.3);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.5);
}

/* 输入框自动填充样式修复 */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 30px white inset !important;
  -webkit-text-fill-color: #111827 !important;
  transition: background-color 5000s ease-in-out 0s;
}

/* 暗色主题下的自动填充样式 */
.dark input:-webkit-autofill,
.dark input:-webkit-autofill:hover,
.dark input:-webkit-autofill:focus,
.dark input:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 30px #374151 inset !important;
  -webkit-text-fill-color: #f9fafb !important;
  transition: background-color 5000s ease-in-out 0s;
}

/* 移动端安全区域适配 */
@supports (padding: max(0px)) {
  .safe-area-inset-top {
    padding-top: max(1rem, env(safe-area-inset-top));
  }

  .safe-area-inset-bottom {
    padding-bottom: max(1rem, env(safe-area-inset-bottom));
  }

  .safe-area-inset-left {
    padding-left: max(1rem, env(safe-area-inset-left));
  }

  .safe-area-inset-right {
    padding-right: max(1rem, env(safe-area-inset-right));
  }
}
