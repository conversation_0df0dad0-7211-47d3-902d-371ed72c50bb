'use client'

/**
 * 简单的缓存测试页面
 * 用于验证缓存监控功能
 */

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/data-display/card'
import { Button } from '@/components/ui/forms/button'

export default function CacheTestPage() {
  const [loading, setLoading] = useState(false)
  const [results, setResults] = useState<any[]>([])

  // 测试 API 端点
  const testEndpoints = [
    { name: '标签 API', url: '/api/tags' },
    { name: '用户货币 API', url: '/api/user/currencies' },
    { name: '树状结构 API', url: '/api/tree-structure' },
  ]

  // 调用 API 并记录结果
  const testApi = async (endpoint: { name: string; url: string }) => {
    setLoading(true)
    const startTime = performance.now()
    
    try {
      const response = await fetch(endpoint.url)
      const endTime = performance.now()
      const duration = endTime - startTime
      
      const result = {
        name: endpoint.name,
        url: endpoint.url,
        status: response.status,
        duration: duration.toFixed(2),
        success: response.ok,
        timestamp: new Date().toLocaleTimeString(),
      }
      
      setResults(prev => [result, ...prev.slice(0, 9)]) // 保留最近10条记录
      
      if (response.ok) {
        console.log(`✅ ${endpoint.name} 调用成功 - ${duration.toFixed(2)}ms`)
      } else {
        console.log(`❌ ${endpoint.name} 调用失败 - ${response.status}`)
      }
    } catch (error) {
      const endTime = performance.now()
      const duration = endTime - startTime
      
      const result = {
        name: endpoint.name,
        url: endpoint.url,
        status: 0,
        duration: duration.toFixed(2),
        success: false,
        timestamp: new Date().toLocaleTimeString(),
        error: error instanceof Error ? error.message : '未知错误',
      }
      
      setResults(prev => [result, ...prev.slice(0, 9)])
      console.error(`❌ ${endpoint.name} 调用异常:`, error)
    } finally {
      setLoading(false)
    }
  }

  // 批量测试所有 API
  const testAllApis = async () => {
    for (const endpoint of testEndpoints) {
      await testApi(endpoint)
      // 等待一小段时间再调用下一个
      await new Promise(resolve => setTimeout(resolve, 500))
    }
  }

  // 获取缓存统计
  const getCacheStats = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/dev/cache-stats')
      if (response.ok) {
        const data = await response.json()
        console.log('📊 缓存统计:', data.data)
        alert('缓存统计已输出到控制台，请查看开发者工具')
      } else {
        alert('获取缓存统计失败')
      }
    } catch (error) {
      console.error('获取缓存统计失败:', error)
      alert('获取缓存统计失败')
    } finally {
      setLoading(false)
    }
  }

  if (process.env.NODE_ENV !== 'development') {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="p-6">
            <p className="text-center text-gray-600">
              此页面仅在开发环境下可用
            </p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">缓存功能测试</h1>
        <div className="flex items-center gap-2">
          <Button
            onClick={getCacheStats}
            disabled={loading}
            variant="outline"
            size="sm"
          >
            获取缓存统计
          </Button>
        </div>
      </div>

      {/* 测试说明 */}
      <Card>
        <CardHeader>
          <CardTitle>测试说明</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm text-gray-600">
            <p>• 点击下方按钮测试各个 API 端点</p>
            <p>• 多次调用同一 API 可以测试缓存命中情况</p>
            <p>• 请观察浏览器控制台的缓存日志输出</p>
            <p>• 使用"获取缓存统计"查看详细的性能数据</p>
          </div>
        </CardContent>
      </Card>

      {/* API 测试按钮 */}
      <Card>
        <CardHeader>
          <CardTitle>API 测试</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {testEndpoints.map((endpoint) => (
              <Button
                key={endpoint.url}
                onClick={() => testApi(endpoint)}
                disabled={loading}
                className="h-auto p-4 flex flex-col items-center gap-2"
              >
                <span className="font-medium">{endpoint.name}</span>
                <span className="text-xs opacity-75">{endpoint.url}</span>
              </Button>
            ))}
            <Button
              onClick={testAllApis}
              disabled={loading}
              variant="outline"
              className="h-auto p-4 flex flex-col items-center gap-2"
            >
              <span className="font-medium">测试所有 API</span>
              <span className="text-xs opacity-75">批量调用</span>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 调用结果 */}
      {results.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>调用结果</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {results.map((result, index) => (
                <div
                  key={index}
                  className={`p-3 rounded-lg border ${
                    result.success
                      ? 'border-green-200 bg-green-50'
                      : 'border-red-200 bg-red-50'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <span className={`text-sm font-medium ${
                        result.success ? 'text-green-700' : 'text-red-700'
                      }`}>
                        {result.success ? '✅' : '❌'} {result.name}
                      </span>
                      <span className="text-xs text-gray-500">
                        {result.timestamp}
                      </span>
                    </div>
                    <div className="flex items-center gap-4 text-sm">
                      <span>状态: {result.status}</span>
                      <span>耗时: {result.duration}ms</span>
                    </div>
                  </div>
                  {result.error && (
                    <div className="mt-2 text-xs text-red-600">
                      错误: {result.error}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 使用提示 */}
      <Card>
        <CardHeader>
          <CardTitle>监控提示</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm text-gray-600">
            <p><strong>控制台日志:</strong> 打开浏览器开发者工具查看缓存命中日志</p>
            <p><strong>缓存命中:</strong> 🎯 绿色标志表示缓存命中，响应时间通常 &lt; 5ms</p>
            <p><strong>缓存未命中:</strong> ❌ 黄色标志表示缓存未命中，需要查询数据库</p>
            <p><strong>性能分析:</strong> 点击"获取缓存统计"查看详细的性能数据和优化建议</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
