'use client'

import { useState } from 'react'
import { useToast } from '@/contexts/providers/ToastContext'
import { useLanguage } from '@/contexts/providers/LanguageContext'
import { useUserCurrencyFormatter } from '@/hooks/useUserCurrencyFormatter'
import { useUserDateFormatter } from '@/hooks/useUserDateFormatter'
import { LoadingSpinnerSVG } from '@/components/ui/feedback/LoadingSpinner'
import ConfirmationModal from '@/components/ui/feedback/ConfirmationModal'
import { Z_INDEX } from '@/lib/constants/dimensions'
import type { ExchangeRateData } from '@/types/core'

interface ExchangeRateListProps {
  exchangeRates: ExchangeRateData[]
  onEdit: (rate: ExchangeRateData) => void
  onDelete: (rateId: string) => void
  onRefresh: () => void
}

export default function ExchangeRateList({
  exchangeRates,
  onEdit,
  onDelete,
  onRefresh,
}: ExchangeRateListProps) {
  const { t } = useLanguage()
  const { formatNumber } = useUserCurrencyFormatter()
  const { formatDate: formatUserDate } = useUserDateFormatter()
  const { showSuccess, showError } = useToast()
  const [deletingId, setDeletingId] = useState<string | null>(null)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [rateToDelete, setRateToDelete] = useState<string | null>(null)

  const handleDelete = async () => {
    if (!rateToDelete) return

    setDeletingId(rateToDelete)

    try {
      const response = await fetch(`/api/exchange-rates/${rateToDelete}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        showSuccess(t('success.deleted'), t('exchange.rate.deleted'))
        onDelete(rateToDelete)
        setShowDeleteConfirm(false)
        setRateToDelete(null)
      } else {
        const data = await response.json()
        showError(t('error.delete.failed'), data.error || t('error.unknown'))
      }
    } catch (error) {
      console.error('Failed to delete exchange rate:', error)
      showError(t('error.delete.failed'), t('error.network'))
    } finally {
      setDeletingId(null)
    }
  }

  const handleDeleteClick = (rateId: string) => {
    setRateToDelete(rateId)
    setShowDeleteConfirm(true)
  }

  const handleDeleteCancel = () => {
    setShowDeleteConfirm(false)
    setRateToDelete(null)
  }

  const formatDate = (dateString: string) => {
    return formatUserDate(new Date(dateString))
  }

  const formatRate = (rate: number) => {
    return formatNumber(rate, 4)
  }

  // 分离输入汇率（USER和API）和自动生成的汇率（AUTO）
  const inputRates = exchangeRates.filter(
    rate => (rate as any).type === 'USER' || (rate as any).type === 'API'
  )
  const autoGeneratedRates = exchangeRates.filter(
    rate => (rate as any).type === 'AUTO'
  )

  if (exchangeRates.length === 0) {
    return (
      <div className='text-center py-8 text-gray-500 dark:text-gray-400'>
        <div className='text-4xl mb-4'>💱</div>
        <p className='text-lg font-medium mb-2'>
          {t('exchange.rate.empty.title')}
        </p>
        <p className='text-sm'>{t('exchange.rate.empty.description')}</p>
      </div>
    )
  }

  // 渲染汇率表格的辅助函数
  const renderRateTable = (
    rates: typeof exchangeRates,
    title: string,
    showActions: boolean = true
  ) => (
    <div className='bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700'>
      <div className='px-4 py-5 sm:p-6'>
        <div className='flex justify-between items-center mb-4'>
          <div>
            <h4 className='text-lg font-medium text-gray-900 dark:text-gray-100'>
              {title}
            </h4>
            <p className='text-sm text-gray-500 dark:text-gray-400 mt-1'>
              {t('exchange.rate.count', { count: rates.length })}
            </p>
          </div>
          {title === t('exchange.rate.input.rates') && (
            <button
              onClick={onRefresh}
              className='p-1 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded transition-colors'
              title={t('common.refresh')}
            >
              <svg
                className='w-4 h-4'
                fill='none'
                stroke='currentColor'
                viewBox='0 0 24 24'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={2}
                  d='M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15'
                />
              </svg>
            </button>
          )}
        </div>

        {rates.length === 0 ? (
          <div className='text-center py-8 text-gray-500 dark:text-gray-400'>
            <p className='text-sm'>
              {title === t('exchange.rate.input.rates')
                ? t('exchange.rate.no.input.rates')
                : t('exchange.rate.no.auto.rates')}
            </p>
          </div>
        ) : (
          <div className='overflow-x-auto'>
            <table className='min-w-full divide-y divide-gray-200 dark:divide-gray-700'>
              <thead className='bg-gray-50 dark:bg-gray-700'>
                <tr>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider'>
                    {t('exchange.rate.currency.pair')}
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider'>
                    {t('exchange.rate.rate')}
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider'>
                    {t('exchange.rate.effective.date')}
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider'>
                    {t('exchange.rate.type')}
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider'>
                    {t('exchange.rate.notes')}
                  </th>
                  {showActions && (
                    <th className='px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider'>
                      {t('common.actions')}
                    </th>
                  )}
                </tr>
              </thead>
              <tbody className='bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700'>
                {rates.map(rate => (
                  <tr
                    key={rate.id}
                    className='hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors'
                  >
                    <td className='px-6 py-4 whitespace-nowrap'>
                      <div className='flex items-center'>
                        <div>
                          <div className='text-sm font-medium text-gray-900 dark:text-gray-100'>
                            {rate.fromCurrencyRef.symbol}{' '}
                            {rate.fromCurrencyRef.name}
                          </div>
                          <div className='text-sm text-gray-500 dark:text-gray-400'>
                            → {rate.toCurrencyRef.symbol}{' '}
                            {rate.toCurrencyRef.name}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className='px-6 py-4 whitespace-nowrap'>
                      <div className='text-sm text-gray-900 dark:text-gray-100 font-mono'>
                        1 {rate.fromCurrency} = {formatRate(rate.rate)}{' '}
                        {rate.toCurrency}
                      </div>
                    </td>
                    <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100'>
                      {formatDate(rate.effectiveDate)}
                    </td>
                    <td className='px-6 py-4 whitespace-nowrap text-sm'>
                      <span
                        className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                          (rate as any).type === 'USER'
                            ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                            : (rate as any).type === 'API'
                              ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                              : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                        }`}
                      >
                        {(rate as any).type === 'USER'
                          ? t('exchange.rate.type.user')
                          : (rate as any).type === 'API'
                            ? t('exchange.rate.type.api')
                            : t('exchange.rate.type.auto')}
                      </span>
                    </td>
                    <td className='px-6 py-4 text-sm text-gray-500 dark:text-gray-400'>
                      <div
                        className='max-w-xs truncate'
                        title={rate.notes || undefined}
                      >
                        {rate.notes || '-'}
                      </div>
                    </td>
                    {showActions && (
                      <td className='px-6 py-4 whitespace-nowrap text-right text-sm font-medium'>
                        <div className='flex justify-end space-x-1'>
                          <button
                            onClick={() => onEdit(rate)}
                            className='p-1 text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded transition-colors'
                            title={t('common.edit')}
                          >
                            <svg
                              className='w-4 h-4'
                              fill='none'
                              stroke='currentColor'
                              viewBox='0 0 24 24'
                            >
                              <path
                                strokeLinecap='round'
                                strokeLinejoin='round'
                                strokeWidth={2}
                                d='M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z'
                              />
                            </svg>
                          </button>
                          <button
                            onClick={() => handleDeleteClick(rate.id)}
                            className='p-1 text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/20 rounded transition-colors disabled:opacity-50'
                            disabled={deletingId === rate.id}
                            title={
                              deletingId === rate.id
                                ? t('common.deleting')
                                : t('common.delete')
                            }
                          >
                            {deletingId === rate.id ? (
                              <LoadingSpinnerSVG size='sm' />
                            ) : (
                              <svg
                                className='w-4 h-4'
                                fill='none'
                                stroke='currentColor'
                                viewBox='0 0 24 24'
                              >
                                <path
                                  strokeLinecap='round'
                                  strokeLinejoin='round'
                                  strokeWidth={2}
                                  d='M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16'
                                />
                              </svg>
                            )}
                          </button>
                        </div>
                      </td>
                    )}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  )

  return (
    <div className='space-y-6'>
      {/* 输入汇率 */}
      {renderRateTable(inputRates, t('exchange.rate.input.rates'), true)}

      {/* 自动生成汇率 */}
      {autoGeneratedRates.length > 0 && (
        <>
          <div className='flex items-center'>
            <div className='flex-grow border-t border-gray-300 dark:border-gray-600'></div>
            <span className='flex-shrink mx-4 text-sm text-gray-500 dark:text-gray-400'>
              {t('exchange.rate.auto.generated.rates')}
            </span>
            <div className='flex-grow border-t border-gray-300 dark:border-gray-600'></div>
          </div>
          {renderRateTable(
            autoGeneratedRates,
            t('exchange.rate.auto.generated.rates'),
            false
          )}
        </>
      )}

      {/* 删除确认对话框 */}
      <ConfirmationModal
        isOpen={showDeleteConfirm}
        title={t('exchange.rate.delete.confirm.title')}
        message={t('exchange.rate.delete.confirm.message')}
        confirmLabel={
          deletingId === rateToDelete
            ? t('common.deleting')
            : t('exchange.rate.delete.confirm')
        }
        cancelLabel={t('common.cancel')}
        onConfirm={handleDelete}
        onCancel={handleDeleteCancel}
        variant='danger'
        zIndex={Z_INDEX.MAX}
      />
    </div>
  )
}
