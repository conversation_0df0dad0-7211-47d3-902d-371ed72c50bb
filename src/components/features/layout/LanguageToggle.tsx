'use client'

import { useLanguage } from '@/contexts/providers/LanguageContext'
import { useState, useEffect } from 'react'
import { Language } from '@/types/core/constants'

interface LanguageToggleProps {
  className?: string
}

export default function LanguageToggle({
  className = '',
}: LanguageToggleProps) {
  const { language, setLanguage, t } = useLanguage()
  const [mounted, setMounted] = useState(false)

  // 防止水合不匹配
  useEffect(() => {
    setMounted(true)
  }, [])

  const toggleLanguage = () => {
    const newLanguage = language === Language.ZH ? Language.EN : Language.ZH
    setLanguage(newLanguage)
  }

  const getLanguageLabel = (lang: Language) => {
    return lang === Language.ZH ? '中文' : 'English'
  }

  const getLanguageIcon = (lang: Language) => {
    if (lang === Language.ZH) {
      return (
        <svg className='w-4 h-4' viewBox='0 0 24 24' fill='currentColor'>
          <path d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z' />
        </svg>
      )
    } else {
      return (
        <svg className='w-4 h-4' viewBox='0 0 24 24' fill='currentColor'>
          <path d='M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zm6.93 6h-2.95c-.32-1.25-.78-2.45-1.38-3.56 1.84.63 3.37 1.91 4.33 3.56zM12 4.04c.83 1.2 1.48 2.53 1.91 3.96h-3.82c.43-1.43 1.08-2.76 1.91-3.96zM4.26 14C4.1 13.36 4 12.69 4 12s.1-1.36.26-2h3.38c-.08.66-.14 1.32-.14 2 0 .68.06 1.34.14 2H4.26zm.82 2h2.95c.32 1.25.78 2.45 1.38 3.56-1.84-.63-3.37-1.9-4.33-3.56zm2.95-8H5.08c.96-1.66 2.49-2.93 4.33-3.56C8.81 5.55 8.35 6.75 8.03 8zM12 19.96c-.83-1.2-1.48-2.53-1.91-3.96h3.82c-.43 1.43-1.08 2.76-1.91 3.96zM14.34 14H9.66c-.09-.66-.16-1.32-.16-2 0-.68.07-1.35.16-2h4.68c.09.65.16 1.32.16 2 0 .68-.07 1.34-.16 2zm.25 5.56c.6-1.11 1.06-2.31 1.38-3.56h2.95c-.96 1.65-2.49 2.93-4.33 3.56zM16.36 14c.08-.66.14-1.32.14-2 0-.68-.06-1.34-.14-2h3.38c.16.64.26 1.31.26 2s-.1 1.36-.26 2h-3.38z' />
        </svg>
      )
    }
  }

  // 在客户端水合完成前显示默认状态
  if (!mounted) {
    return (
      <button
        className={`
          p-2 rounded-lg text-gray-400 hover:text-gray-600 hover:bg-gray-50
          dark:text-gray-400 dark:hover:text-gray-300 dark:hover:bg-gray-800
          transition-all duration-200 group relative
          ${className}
        `}
        disabled
      >
        <div className='flex items-center space-x-1'>
          <svg className='w-4 h-4' viewBox='0 0 24 24' fill='currentColor'>
            <path d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z' />
          </svg>
          <span className='text-xs font-medium hidden sm:block'>ZH</span>
        </div>
      </button>
    )
  }

  return (
    <button
      onClick={toggleLanguage}
      className={`
        p-2 rounded-lg text-gray-400 hover:text-gray-600 hover:bg-gray-50
        dark:text-gray-400 dark:hover:text-gray-300 dark:hover:bg-gray-800
        transition-all duration-200 group relative
        ${className}
      `}
      title={`${t('common.switch.to')} ${getLanguageLabel(language === Language.ZH ? Language.EN : Language.ZH)}`}
    >
      <div className='flex items-center space-x-1'>
        {getLanguageIcon(language as Language)}
        <span className='text-xs font-medium hidden sm:block'>
          {language.toUpperCase()}
        </span>
      </div>

      {/* 悬停提示 */}
      <div className='absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 dark:bg-gray-700 text-white dark:text-gray-200 text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap'>
        {getLanguageLabel(language === Language.ZH ? Language.EN : Language.ZH)}
      </div>
    </button>
  )
}
