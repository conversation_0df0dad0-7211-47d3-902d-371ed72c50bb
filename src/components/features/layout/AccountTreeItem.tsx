'use client'

import { useState, useRef } from 'react'
import { usePathname } from 'next/navigation'
import AccountContextMenu from './AccountContextMenu'
import DeleteConfirmModal from '@/components/ui/feedback/DeleteConfirmModal'
import CategorySelector from '@/components/ui/forms/CategorySelector'
import AccountSettingsModal from '@/components/ui/feedback/AccountSettingsModal'
import BalanceUpdateModal from '@/components/features/accounts/BalanceUpdateModal'
import FlowTransactionModal from '@/components/features/accounts/FlowTransactionModal'
import { useToast } from '@/contexts/providers/ToastContext'
import { useUserData } from '@/contexts/providers/UserDataContext'
import { useLanguage } from '@/contexts/providers/LanguageContext'
import { useUserCurrencyFormatter } from '@/hooks/useUserCurrencyFormatter'
import { useOptimizedNavigation } from '@/hooks/ui/useOptimizedNavigation'
import {
  publishAccountDelete,
  publishAccountUpdate,
} from '@/lib/services/data-update.service'
import CurrencyTag from '@/components/ui/data-display/CurrencyTag'
import { CURRENCY_SYMBOLS } from '@/types/core/constants'
import type { AccountTreeItemProps } from '@/types/components'
import type { SimpleAccount } from '@/types/core'

export default function AccountTreeItem({
  account,
  level,
  onNavigate,
  onDataChange,
  baseCurrency: propBaseCurrency,
}: AccountTreeItemProps) {
  const { showSuccess, showError } = useToast()
  const { t } = useLanguage()
  const { formatCurrency } = useUserCurrencyFormatter()
  const pathname = usePathname()
  const { navigateTo } = useOptimizedNavigation()
  const moreButtonRef = useRef<HTMLButtonElement | null>(null)

  // 使用UserDataContext获取数据
  const { currencies, tags, getBaseCurrency, removeAccount, updateAccount } =
    useUserData()

  // 移除自动交易检查，改为在后端验证

  // 使用传入的基础货币或从Context获取
  const baseCurrency = propBaseCurrency ||
    getBaseCurrency() || { symbol: CURRENCY_SYMBOLS.CNY, code: 'CNY' }

  // 模态框状态
  const [showContextMenu, setShowContextMenu] = useState(false)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [showCategorySelector, setShowCategorySelector] = useState(false)
  const [showSettingsModal, setShowSettingsModal] = useState(false)
  const [showBalanceUpdateModal, setShowBalanceUpdateModal] = useState(false)
  const [showTransactionModal, setShowTransactionModal] = useState(false)

  const isActive = pathname === `/accounts/${account.id}`

  // 使用传入的余额数据
  const balance = account.balanceInBaseCurrency || 0
  const _currencySymbol = baseCurrency?.symbol || CURRENCY_SYMBOLS.CNY

  // 获取账户原始货币的余额（用于余额更新模态框）
  const accountCurrency = account.currency?.code || baseCurrency?.code || 'USD'
  const accountBalance = account.balances?.[accountCurrency]?.amount || 0

  // 根据账户类型确定金额颜色
  const getAmountColor = () => {
    const accountType = account.category.type
    if (accountType === 'LIABILITY') {
      return 'text-orange-600'
    } else if (accountType === 'EXPENSE') {
      return 'text-red-600'
    } else {
      return 'text-green-600'
    }
  }

  // 生成账户颜色的透明背景色用于hover效果
  const _getAccountHoverStyle = () => {
    if (!account.color) {
      return 'hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100 dark:hover:from-gray-800/50 dark:hover:to-gray-700/30'
    }

    // 将hex颜色转换为RGB并添加透明度
    const hexToRgb = (hex: string) => {
      const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
      return result
        ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16),
          }
        : null
    }

    const rgb = hexToRgb(account.color)
    if (!rgb) {
      return 'hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100 dark:hover:from-gray-800/50 dark:hover:to-gray-700/30'
    }

    // 创建透明背景色
    const _lightBg = `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.08)`
    const _darkBg = `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.15)`

    return 'hover:bg-gradient-to-r'
  }

  // 获取账户颜色的内联样式
  const getAccountInlineStyle = () => {
    if (!account.color) return {}

    const hexToRgb = (hex: string) => {
      const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
      return result
        ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16),
          }
        : null
    }

    const rgb = hexToRgb(account.color)
    if (!rgb) return {}

    // 计算更深的颜色用于文字
    const darkerRgb = {
      r: Math.max(0, rgb.r - 40),
      g: Math.max(0, rgb.g - 40),
      b: Math.max(0, rgb.b - 40),
    }

    return {
      '--hover-bg-from': `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.08)`,
      '--hover-bg-to': `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.12)`,
      '--active-bg-from': `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.15)`,
      '--active-bg-to': `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.20)`,
      '--active-border': `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.3)`,
      '--active-text-color': `rgb(${darkerRgb.r}, ${darkerRgb.g}, ${darkerRgb.b})`,
    } as React.CSSProperties
  }

  // 获取选中状态的文字颜色
  const getActiveTextColor = () => {
    if (!isActive) return ''

    if (account.color) {
      return 'account-active-text'
    } else {
      return 'text-blue-700 dark:text-blue-300'
    }
  }

  // 数据现在从UserDataContext获取，无需额外的API调用

  const handleContextMenu = (e: React.MouseEvent) => {
    e.preventDefault()
    setShowContextMenu(true)
  }

  const handleMenuAction = (action: string) => {
    setShowContextMenu(false)

    switch (action) {
      case 'view-details':
        navigateTo(`/accounts/${account.id}`)
        onNavigate?.()
        break
      case 'add-transaction':
        setShowTransactionModal(true)
        break
      case 'update-balance':
        setShowBalanceUpdateModal(true)
        break
      case 'move':
        setShowCategorySelector(true)
        break
      case 'settings':
        setShowSettingsModal(true)
        break
      case 'delete':
        setShowDeleteConfirm(true)
        break
      default:
        console.log(`Unknown action: ${action}`)
    }
  }

  const handleDelete = async () => {
    try {
      const response = await fetch(`/api/accounts/${account.id}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        // 从Context中移除账户
        removeAccount(account.id)
        setShowDeleteConfirm(false)
        showSuccess(
          t('account.delete.success'),
          t('account.delete.success.message', { name: account.name })
        )

        // 发布账户删除事件
        await publishAccountDelete(account.id, account.categoryId, {
          deletedAccount: account,
        })

        // 通知父组件数据已更新
        onDataChange?.({ type: 'account' })

        // 账户删除事件已发布，树会自动更新
      } else {
        const error = await response.json()
        console.error('Account deletion failed:', {
          status: response.status,
          statusText: response.statusText,
          error: error,
        })

        // 正确获取错误信息：API返回的是 error.error 而不是 error.message
        const errorMessage =
          error.error || error.message || t('account.move.unknown.error')
        showError(t('account.delete.failed'), errorMessage)
      }
    } catch (error) {
      console.error('Error deleting account:', error)
      showError(t('account.delete.failed'), t('account.delete.network.error'))
    }
  }

  const handleClearBalanceHistory = async () => {
    try {
      const response = await fetch(
        `/api/accounts/${account.id}/clear-balance`,
        {
          method: 'DELETE',
        }
      )

      if (response.ok) {
        const result = await response.json()
        showSuccess(
          t('account.clear.success'),
          result.message || t('account.clear.default.message')
        )

        // 清空成功后，直接删除账户
        await handleDelete()
      } else {
        const error = await response.json()
        console.error('Clear balance history failed:', {
          status: response.status,
          statusText: response.statusText,
          error: error,
        })

        // 正确获取错误信息：API返回的是 error.error 而不是 error.message
        const errorMessage =
          error.error ||
          error.message ||
          t('account.balance.history.clear.failed')
        showError(t('account.clear.failed'), errorMessage)
      }
    } catch (error) {
      console.error('Error clearing balance history:', error)
      showError(t('account.clear.failed'), t('account.clear.network.error'))
    }
  }

  const handleMoveToCategory = async (newCategoryId: string) => {
    try {
      const response = await fetch(`/api/accounts/${account.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          categoryId: newCategoryId, // 只更新分类ID
        }),
      })

      if (response.ok) {
        setShowCategorySelector(false)
        await publishAccountUpdate(account.id, newCategoryId, {
          updatedAccount: { ...account, categoryId: newCategoryId },
          originalCategoryId: account.categoryId,
        })

        // 通知父组件数据已更新
        onDataChange?.({ type: 'account' })
      } else {
        const error = await response.json()
        showError(
          t('account.move.failed'),
          error.message || t('account.move.unknown.error')
        )
      }
    } catch (error) {
      console.error('Error moving account:', error)
      showError(t('account.move.failed'), t('account.move.network.error'))
    }
  }

  const handleBalanceUpdateSuccess = () => {
    // BalanceUpdateModal 内部会发布 balance-update 事件
    setShowBalanceUpdateModal(false)
  }

  const handleTransactionSuccess = () => {
    // FlowTransactionModal 内部会发布 transaction-create/update 事件
    setShowTransactionModal(false)
  }

  const handleSaveSettings = async (updates: Partial<SimpleAccount>) => {
    try {
      const response = await fetch(`/api/accounts/${account.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: updates.name || account.name,
          categoryId: account.categoryId,
          description: updates.description,
          color: updates.color,
          currencyId: updates.currencyId,
        }),
      })

      if (response.ok) {
        // 更新UserDataContext中的账户数据
        const result = await response.json()
        if (result.data) {
          // 转换API返回的数据格式以匹配UserDataContext期望的格式
          const updatedAccountData = {
            ...result.data,
            currencyCode: result.data.currency?.code || account.currency?.code,
            userId: result.data.userId,
          }
          updateAccount(updatedAccountData)
        }

        // 发布账户更新事件
        await publishAccountUpdate(account.id, result.data.categoryId, {
          updatedAccount: result.data,
          originalAccount: account,
        })

        // 通知父组件数据已更新
        onDataChange?.({ type: 'account' })

        showSuccess(t('success.saved'), t('account.settings.saved'))
      } else {
        const error = await response.json()
        throw new Error(error.message || '保存失败')
      }
    } catch (error) {
      console.error('Error saving account settings:', error)
      throw error
    }
  }

  return (
    <div className='relative'>
      <div
        className={`
          flex items-center group rounded-lg transition-all duration-200 cursor-pointer
          mx-1 my-0.5 border border-transparent
          ${
            isActive
              ? account.color
                ? 'account-active-item shadow-sm'
                : 'bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/30 dark:to-blue-800/20 border-blue-200 dark:border-blue-700/50 shadow-sm'
              : account.color
                ? 'account-hover-item hover:shadow-sm hover:border-gray-200 dark:hover:border-gray-600/50'
                : 'hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100 dark:hover:from-gray-800/50 dark:hover:to-gray-700/30 hover:border-gray-200 dark:hover:border-gray-600/50 hover:shadow-sm'
          }
        `}
        style={{
          paddingLeft: `${level * 16 + 20}px`,
          ...getAccountInlineStyle(),
        }}
        onClick={e => {
          e.preventDefault()
          // 添加轻微的过渡效果，减少视觉跳跃
          const target = e.currentTarget
          target.style.transform = 'scale(0.98)'
          target.style.transition = 'transform 0.1s ease-out'

          setTimeout(() => {
            target.style.transform = ''
            navigateTo(`/accounts/${account.id}`)
            onNavigate?.()
          }, 50)
        }}
      >
        {/* 货币标签 */}
        <div className='mr-3 flex-shrink-0'>
          <CurrencyTag
            currencyCode={account.currency?.code || 'USD'}
            color={account.color || undefined}
            size='sm'
          />
        </div>

        {/* 账户名称和余额 */}
        <div className='flex-1 py-3 min-w-0'>
          <div
            className={`
              text-sm font-medium truncate transition-colors duration-200
              ${
                isActive
                  ? getActiveTextColor()
                  : 'text-gray-700 dark:text-gray-300 group-hover:text-gray-900 dark:group-hover:text-gray-100'
              }
            `}
            title={account.description || account.name}
          >
            {account.name}
          </div>
          {balance !== null && (
            <div
              className={`text-xs mt-1.5 font-semibold transition-colors duration-200 ${getAmountColor()}`}
            >
              {formatCurrency(Math.abs(balance), baseCurrency?.code || 'CNY')}
            </div>
          )}
        </div>

        {/* 更多操作按钮 */}
        <button
          ref={moreButtonRef}
          onClick={e => {
            e.preventDefault()
            e.stopPropagation()
            setShowContextMenu(true)
          }}
          onContextMenu={handleContextMenu}
          className='mr-3 p-2 rounded-lg transition-all duration-200 opacity-0 group-hover:opacity-100 hover:bg-white/80 dark:hover:bg-gray-800/80 hover:shadow-md'
          title={t('account.more.actions')}
        >
          <svg
            className='h-4 w-4 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 transition-colors duration-200'
            fill='none'
            stroke='currentColor'
            viewBox='0 0 24 24'
          >
            <path
              strokeLinecap='round'
              strokeLinejoin='round'
              strokeWidth={2}
              d='M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z'
            />
          </svg>
        </button>
      </div>

      {/* 上下文菜单 */}
      <AccountContextMenu
        isOpen={showContextMenu}
        onClose={() => setShowContextMenu(false)}
        onAction={handleMenuAction}
        account={account}
        triggerRef={moreButtonRef}
      />

      {/* 删除确认对话框 */}
      <DeleteConfirmModal
        isOpen={showDeleteConfirm}
        title={t('account.delete.title')}
        itemName={account.name}
        itemType={t('account.delete.item.type')}
        onConfirm={handleDelete}
        onCancel={() => setShowDeleteConfirm(false)}
        hasRelatedData={
          account.category?.type === 'ASSET' ||
          account.category?.type === 'LIABILITY'
        }
        relatedDataMessage={t('account.balance.related.data.message')}
        onClearRelatedData={handleClearBalanceHistory}
        clearDataLabel={t('account.clear.balance.and.delete')}
      />

      {/* 分类选择器 */}
      <CategorySelector
        isOpen={showCategorySelector}
        title={t('account.move.to.category')}
        currentCategoryId={account.categoryId}
        filterByAccountType={account.category.type}
        onSelect={handleMoveToCategory}
        onCancel={() => setShowCategorySelector(false)}
      />

      {/* 账户设置模态框 */}
      <AccountSettingsModal
        isOpen={showSettingsModal}
        onClose={() => setShowSettingsModal(false)}
        onSave={handleSaveSettings}
        account={account}
        currencies={currencies}
      />

      {/* 余额更新模态框 */}
      <BalanceUpdateModal
        isOpen={showBalanceUpdateModal}
        onClose={() => setShowBalanceUpdateModal(false)}
        onSuccess={handleBalanceUpdateSuccess}
        account={account}
        currencies={currencies}
        currentBalance={accountBalance || 0}
        currencyCode={accountCurrency}
      />

      {/* 简化的流量账户交易表单模态框 */}
      <FlowTransactionModal
        isOpen={showTransactionModal}
        onClose={() => setShowTransactionModal(false)}
        onSuccess={handleTransactionSuccess}
        account={account}
        currencies={currencies}
        tags={tags}
      />
    </div>
  )
}
