# Flow Balance - Docker ignore file

# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Next.js
.next/
out/
build/
dist/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Database files
*.db
*.db-journal
prisma/dev.db*
prisma/test.db*

# Testing
coverage/
.nyc_output

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Documentation and guides
docs/
CODE_GUIDE_DOC/
*.md
!README.md

# Git
.git
.gitignore

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Scripts (keep only essential ones)
scripts/
!scripts/docker-entrypoint.sh

# Development backup files
dev-files-backup-*/

# Data directories
data/

# Test files
**/*.test.js
**/*.test.ts
**/*.spec.js
**/*.spec.ts
__tests__/
__mocks__/

# Linting and formatting
.eslintrc*
.prettierrc*
.editorconfig

# TypeScript
*.tsbuildinfo
# Keep tsconfig.json for path mapping
!tsconfig.json

# Husky
.husky/

# Temporary files
tmp/
temp/
