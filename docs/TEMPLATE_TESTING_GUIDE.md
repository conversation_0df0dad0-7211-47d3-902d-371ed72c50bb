# 🧪 交易模板功能测试指南

## 📋 测试概述

本文档提供了交易模板功能的详细测试指南，确保所有功能按预期工作。

## 🎯 测试环境

- **应用地址**: http://localhost:3001
- **测试账户**: 需要已登录的用户账户
- **前置条件**:
  - 至少有一个收入账户和一个支出账户
  - 有一些标签可供选择

## 🔍 功能测试清单

### 1. 模板创建测试

#### 1.1 QuickFlowTransactionModal 模板创建

**测试步骤**:

1. 打开仪表板页面
2. 点击快速交易按钮（收入或支出）
3. 填写交易信息：
   - 选择账户
   - 输入金额
   - 输入描述
   - 添加备注（可选）
   - 选择标签（可选）
4. 在"模板名称"字段输入新模板名称
5. 提交交易

**预期结果**:

- 交易创建成功
- 显示模板创建成功的提示
- 模板保存到数据库

#### 1.2 FlowTransactionModal 模板创建

**测试步骤**:

1. 进入账户详情页面
2. 点击"添加交易"按钮
3. 填写交易信息（同上）
4. 在"模板名称"字段输入新模板名称
5. 提交交易

**预期结果**: 同上

### 2. 模板选择和应用测试

#### 2.1 模板选择

**测试步骤**:

1. 打开交易录入模态框
2. 点击"交易模板"下拉框
3. 查看模板列表
4. 使用搜索功能查找模板
5. 选择一个模板

**预期结果**:

- 显示当前用户的模板列表
- 搜索功能正常工作
- 选择模板后自动填充数据（除金额外）
- 模板名称显示在输入框中

#### 2.2 模板数据填充

**测试步骤**:

1. 选择一个已有模板
2. 检查表单字段是否正确填充

**预期结果**:

- 账户自动选择
- 描述自动填充
- 备注自动填充
- 标签自动选择
- 金额字段保持空白
- 日期保持当前日期

### 3. 模板更新测试

#### 3.1 模板数据修改检测

**测试步骤**:

1. 选择一个已有模板
2. 修改描述、备注或标签
3. 观察界面变化

**预期结果**:

- 显示"更新模板数据"确认框
- 默认不勾选更新选项

#### 3.2 模板更新操作

**测试步骤**:

1. 选择模板并修改数据
2. 勾选"是否修改保存此模板"
3. 提交交易

**预期结果**:

- 交易创建成功
- 模板更新成功
- 显示模板更新成功提示

#### 3.3 不更新模板操作

**测试步骤**:

1. 选择模板并修改数据
2. 不勾选"是否修改保存此模板"
3. 提交交易

**预期结果**:

- 交易创建成功
- 模板数据保持不变

### 4. 模板删除测试

#### 4.1 模板删除操作

**测试步骤**:

1. 选择一个已有模板
2. 点击删除按钮（垃圾桶图标）
3. 确认删除

**预期结果**:

- 显示删除确认对话框
- 确认后模板被删除
- 表单数据被清空
- 显示删除成功提示

#### 4.2 模板删除取消

**测试步骤**:

1. 选择一个已有模板
2. 点击删除按钮
3. 取消删除

**预期结果**:

- 模板不被删除
- 表单数据保持不变

### 5. 边界情况测试

#### 5.1 重复模板名称

**测试步骤**:

1. 输入已存在的模板名称
2. 提交交易

**预期结果**:

- 显示错误提示："模板名称已存在"
- 交易创建成功，但模板不被创建

#### 5.2 空模板名称

**测试步骤**:

1. 不输入模板名称
2. 提交交易

**预期结果**:

- 交易创建成功
- 不创建模板

#### 5.3 模板名称长度限制

**测试步骤**:

1. 输入超过100个字符的模板名称
2. 提交交易

**预期结果**:

- 显示错误提示："模板名称不能超过100个字符"

### 6. 用户体验测试

#### 6.1 响应式设计

**测试步骤**:

1. 在不同屏幕尺寸下测试模板功能
2. 测试移动端和桌面端

**预期结果**:

- 在所有设备上正常显示
- 交互元素大小适当

#### 6.2 主题切换

**测试步骤**:

1. 在浅色主题下测试
2. 切换到深色主题测试

**预期结果**:

- 在两种主题下都正常显示
- 颜色和对比度适当

#### 6.3 国际化

**测试步骤**:

1. 在中文环境下测试
2. 切换到英文环境测试

**预期结果**:

- 所有文本正确翻译
- 布局不受语言切换影响

## 🐛 常见问题排查

### 问题1: 模板列表不显示

**可能原因**:

- API 请求失败
- 用户没有创建过模板
- 权限问题

**排查步骤**:

1. 检查浏览器控制台错误
2. 检查网络请求状态
3. 确认用户已登录

### 问题2: 模板数据不填充

**可能原因**:

- 模板数据格式错误
- 账户或分类不存在
- 标签数据问题

**排查步骤**:

1. 检查模板数据结构
2. 确认关联的账户和分类存在
3. 检查标签数据

### 问题3: 模板保存失败

**可能原因**:

- 数据验证失败
- 数据库连接问题
- 权限不足

**排查步骤**:

1. 检查表单数据完整性
2. 查看服务器日志
3. 确认数据库连接正常

## ✅ 测试完成标准

- [ ] 所有基本功能测试通过
- [ ] 边界情况处理正确
- [ ] 用户体验良好
- [ ] 错误处理完善
- [ ] 性能表现良好
- [ ] 兼容性测试通过

## 📊 测试报告模板

```
测试日期: ____
测试人员: ____
测试环境: ____

功能测试结果:
- 模板创建: ✅/❌
- 模板选择: ✅/❌
- 模板更新: ✅/❌
- 模板删除: ✅/❌

发现问题:
1. ____
2. ____

建议改进:
1. ____
2. ____
```

---

**版本**: v1.0  
**更新时间**: 2025-06-18
