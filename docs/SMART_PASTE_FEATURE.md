# 智能粘贴表格功能 (Smart Paste Grid)

## 功能概述

智能粘贴表格是Flow
Balance应用中的一个强大功能，允许用户批量录入交易数据。该功能提供了一个交互式的数据暂存与校验区，用户可以从容地整理、修正数据，直到对每一条数据的正确性都充满信心，然后一键完成最终提交。

## 核心特性

### 🎯 核心理念

**自信录入，一次搞定 (Confident Entry, One-Shot Success)**

### ✨ 主要功能

1. **智能粘贴 (Smart Paste)**

   - 支持从Excel、Google Sheets等外部应用复制数据
   - 自动解析Tab分隔的数据格式
   - 智能创建新行并填充数据

2. **三层实时校验反馈系统**

   - **单元格级反馈**: 输入完成后立即显示绿色(通过)或红色(错误)背景
   - **行级反馈**: 整行数据完整且正确时显示绿色勾选标记
   - **全局级反馈**: 只有所有数据验证通过后才能提交

3. **键盘优先操作**

   - 方向键导航 (↑↓←→)
   - Tab/Shift+Tab 切换单元格
   - Enter 确认并下移
   - Ctrl+C/V 复制粘贴
   - Ctrl+Z/Y 撤销重做
   - Ctrl+D 向下填充

4. **历史记录管理**
   - 支持撤销/重做操作
   - 记录所有编辑历史
   - 可恢复到任意历史状态

## 使用方法

### 1. 打开智能粘贴表格

在账户详情页面，点击"批量录入"按钮即可打开智能粘贴模态框。

### 2. 数据录入方式

#### 方式一：手动输入

- 点击任意单元格开始输入
- 使用Tab键或方向键在单元格间导航
- Enter键确认输入并移动到下一行

#### 方式二：整行粘贴数据

1. 从Excel或其他应用复制多行数据（确保使用Tab分隔）
2. 在表格中选择起始单元格
3. 按Ctrl+V粘贴数据
4. 系统自动创建相应行数并填充数据

#### 方式三：列粘贴数据 ⭐ 新功能

1. 在Excel中选择并复制一整列数据（比如一列日期）
2. 在智能表格中点击对应列的任意单元格
3. 按Ctrl+V粘贴数据
4. 系统会自动：
   - 检测到这是列数据（多行单列）
   - 自动扩展表格行数以容纳所有数据
   - 将数据填充到对应列的连续行中
   - 根据列的数据类型自动转换格式

### 3. 数据验证

系统会实时验证输入的数据：

- **绿色背景**: 数据格式正确
- **红色背景**: 数据有错误，鼠标悬停查看错误信息
- **黄色背景**: 数据可用但建议检查
- **绿色勾选**: 整行数据完整且正确

### 4. 数据提交

只有当所有行都显示绿色勾选时，"提交"按钮才会激活。点击提交后，系统会批量创建交易记录。

## 支持的数据格式

### 基本字段

| 字段 | 类型 | 必填 | 说明             |
| ---- | ---- | ---- | ---------------- |
| 日期 | 日期 | ✓    | 格式：YYYY-MM-DD |
| 金额 | 数字 | ✓    | 必须大于0        |
| 描述 | 文本 | ✓    | 1-200个字符      |
| 备注 | 文本 | ✗    | 最多1000个字符   |
| 账户 | 选择 | ✓    | 从可用账户中选择 |
| 分类 | 选择 | ✓    | 从可用分类中选择 |
| 标签 | 多选 | ✗    | 可选择多个标签   |

### 示例数据

#### 整行粘贴示例（Tab分隔）

```
2024-01-15	50.00	午餐	麦当劳
2024-01-15	25.50	交通	地铁费用
2024-01-16	120.00	购物	超市采购
2024-01-16	8.00	饮料	咖啡
```

#### 列粘贴示例

**日期列数据：**

```
2024-01-15
2024-01-16
2024-01-17
2024-01-18
2024-01-19
```

**金额列数据：**

```
50.00
25.50
120.00
8.00
35.80
```

**描述列数据：**

```
午餐
交通
购物
饮料
加油
```

## 快捷键参考

| 快捷键    | 功能           |
| --------- | -------------- |
| ↑↓←→      | 单元格导航     |
| Tab       | 下一个单元格   |
| Shift+Tab | 上一个单元格   |
| Enter     | 确认输入并下移 |
| Escape    | 取消编辑       |
| Ctrl+C    | 复制           |
| Ctrl+V    | 粘贴           |
| Ctrl+Z    | 撤销           |
| Ctrl+Y    | 重做           |
| Ctrl+D    | 向下填充       |
| F2        | 编辑单元格     |

## 验证规则

### 日期验证

- 必须是有效的日期格式
- 不能早于一年前
- 不能晚于一年后

### 金额验证

- 必须是正数
- 支持小数点
- 自动格式化显示

### 文本验证

- 描述字段：1-200个字符
- 备注字段：最多1000个字符
- 自动去除首尾空格

### 关联数据验证

- 账户必须存在且有权限访问
- 分类必须存在且类型匹配
- 标签必须存在且有权限访问

## 错误处理

### 常见错误及解决方法

1. **"请输入有效的数字"**

   - 检查金额字段是否包含非数字字符
   - 确保使用正确的小数点格式

2. **"请输入有效的日期"**

   - 使用YYYY-MM-DD格式
   - 检查日期是否真实存在

3. **"账户不存在或无权访问"**

   - 确保选择的账户在当前用户的账户列表中
   - 检查账户类型是否匹配

4. **"分类不存在或无权访问"**
   - 确保选择的分类存在
   - 检查分类类型是否与账户类型匹配

## 性能优化

- 支持最多100行批量录入
- 实时验证不会影响输入流畅度
- 智能缓存减少重复计算
- 异步提交避免界面卡顿

## 技术架构

### 组件结构

```
SmartPasteModal
├── SmartPasteGrid
│   ├── SmartPasteRow
│   │   └── SmartPasteCell
│   └── 工具栏和操作按钮
└── 历史记录管理器
```

### 核心模块

- `SmartPasteGrid`: 主表格组件
- `SmartPasteCell`: 可编辑单元格
- `SmartPasteRow`: 表格行组件
- `HistoryManager`: 历史记录管理
- `ValidationUtils`: 数据验证工具
- `DataUtils`: 数据处理工具

## API接口

### 批量创建交易

```
POST /api/transactions/batch
```

**请求体:**

```json
{
  "transactions": [
    {
      "accountId": "string",
      "categoryId": "string",
      "currencyCode": "string",
      "type": "INCOME|EXPENSE",
      "amount": "number",
      "description": "string",
      "notes": "string",
      "date": "string",
      "tagIds": ["string"]
    }
  ]
}
```

**响应:**

```json
{
  "success": true,
  "data": {
    "created": [...],
    "errors": [...],
    "warnings": [...],
    "summary": {
      "total": 10,
      "created": 8,
      "failed": 2
    }
  }
}
```

## 测试页面

访问 `/test-smart-paste` 可以体验智能粘贴功能的完整流程。

## 未来规划

- [ ] 支持CSV文件导入
- [ ] 模板保存和复用
- [ ] 更多数据格式支持
- [ ] 批量编辑功能
- [ ] 数据预览和确认
- [ ] 导入进度显示
- [ ] 错误数据导出
