# Flow Balance - 个人财务管理系统功能规格书

## 📖 项目概述

Flow
Balance 是一个基于现代 Web 技术栈开发的专业个人财务管理系统，采用企业级财务管理理念，正确区分**存量**（资产负债）和**流量**（收入支出）概念，为个人用户提供专业级的财务分析和管理工具。

### 🎯 核心理念

- **存量概念**：资产和负债账户反映特定时点的财务状况
- **流量概念**：收入和支出账户反映特定期间的现金流动
- **专业报表**：提供标准的资产负债表和现金流量表
- **多币种支持**：支持多种货币和汇率管理
- **智能分析**：基于会计学原理的财务数据分析

## 🛠️ 技术栈

### 前端技术

- **框架**：Next.js 15.3.3 (App Router)
- **UI库**：React 19 + TypeScript 5
- **样式**：Tailwind CSS 4
- **图表**：ECharts 5.6.0 + echarts-for-react 3.0.2
- **图标**：Lucide React 0.513.0
- **状态管理**：React Context API
- **数据验证**：Zod 3.25.67

### 后端技术

- **运行时**：Node.js 20+
- **数据库**：SQLite (开发) / PostgreSQL (生产)
- **ORM**：Prisma 6.9.0
- **认证**：JWT + bcryptjs
- **API**：Next.js API Routes
- **日期处理**：date-fns 4.1.0

### 开发工具

- **包管理器**：pnpm
- **代码规范**：ESLint 9 + TypeScript 5
- **构建工具**：Next.js Turbopack
- **测试框架**：Jest 29.7.0 + Testing Library
- **代码格式化**：Prettier 3.5.3
- **Git Hooks**：Husky 8.0.3 + lint-staged 15.5.2

## 📊 数据模型

### 核心实体

1. **用户 (User)**：系统用户信息
2. **用户设置 (UserSettings)**：个人偏好和配置
3. **货币 (Currency)**：全局货币信息
4. **用户货币 (UserCurrency)**：用户可用货币管理
5. **汇率 (ExchangeRate)**：用户自定义汇率
6. **分类 (Category)**：树状结构的账户分类
7. **账户 (Account)**：用户的各种财务账户
8. **交易 (Transaction)**：所有交易记录
9. **标签 (Tag)**：交易标签系统

### 账户类型枚举

- **ASSET**：资产类（存量）- 现金、银行存款、投资、房产等
- **LIABILITY**：负债类（存量）- 信用卡、贷款、应付款等
- **INCOME**：收入类（流量）- 工资、投资收益、其他收入等
- **EXPENSE**：支出类（流量）- 生活费、娱乐、交通等

### 交易类型枚举

- **INCOME**：收入
- **EXPENSE**：支出
- **BALANCE**：余额调整（仅用于存量类账户）

## � 核心功能模块

### 💰 FIRE功能（财务自由计算）

- **CAGR计算**：基于历史净资产变化的复合年增长率计算
- **FIRE目标设定**：支持自定义安全提取率和退休支出
- **可视化预测**：图表展示财务自由路径和时间预测
- **现实快照**：基于真实数据的当前财务状况分析
- **参数调节**：交互式参数调整和实时计算

### 📊 智能粘贴功能

- **批量数据录入**：支持从Excel等外部应用复制粘贴数据
- **三层验证系统**：单元格级、行级、全局级实时验证反馈
- **Excel风格操作**：支持方向键导航、Tab切换、Ctrl+Z撤销等
- **多种粘贴模式**：整行粘贴、列粘贴、单元格粘贴
- **智能数据处理**：自动识别日期、金额、标签等数据类型

### 🔄 循环交易管理

- **定期交易模板**：支持日、周、月、年等多种频率
- **自动生成机制**：按计划自动生成交易记录
- **历史补录**：自动检测并补录遗漏的历史交易
- **重复检测**：防止重复生成相同日期的交易

### 🏦 贷款合约管理

- **多种还款方式**：等额本息、等额本金、仅付利息
- **自动还款计划**：根据合约自动生成还款记录
- **利息计算**：精确的利息计算和本金分摊
- **提前还款**：支持提前还款和重新计算

### 📝 交易模板系统

- **快速录入**：预设常用交易模板
- **模板管理**：创建、编辑、删除交易模板
- **智能应用**：一键应用模板到新交易

## �🔐 用户认证与授权系统

### 认证功能

- **用户注册**：邮箱密码注册，密码哈希存储
- **用户登录**：JWT令牌认证，安全会话管理
- **密码重置**：完整的忘记密码和重置密码流程
- **会话管理**：自动登录状态检查和令牌刷新

### 授权机制

- **数据隔离**：用户只能访问自己的数据
- **API保护**：所有API端点都需要身份验证
- **权限控制**：基于用户ID的数据访问控制

## 🚀 初始设置系统

### 设置流程

1. **货币选择**：从全球货币列表中选择可用货币
2. **本位币设置**：选择主要货币作为汇总计算基准
3. **默认分类创建**：自动创建基础账户分类
4. **引导完成**：跳转到仪表板开始使用

### 特性

- **常用货币推荐**：智能推荐常用货币
- **多选支持**：支持选择多种货币
- **搜索功能**：快速查找特定货币
- **设置验证**：确保配置完整性

## 📈 仪表板系统

### 财务概览

- **净资产总额**：资产减负债的实时计算
- **总资产金额**：所有资产账户余额汇总
- **总负债金额**：所有负债账户余额汇总
- **本月收支情况**：当月收入支出统计

### 账户汇总

- **分类分组显示**：按账户类型分组展示
- **多币种余额汇总**：自动转换为本位币显示
- **实时余额更新**：数据变更实时同步
- **快速操作入口**：便捷的账户管理入口

### 趋势图表

- **净资产趋势图**：显示资产净值变化趋势
- **现金流图表**：显示收支流量变化情况
- **分类分布图**：显示资产/负债分布结构
- **月度对比图**：显示月度财务变化对比

### 数据验证

- **数据质量评分**：自动评估数据完整性
- **验证提醒**：显示数据问题和优化建议
- **业务逻辑检查**：验证交易类型与账户类型匹配

## 🏦 账户管理系统

### 分类体系

- **树状结构**：支持多级分类管理
- **类型继承**：子分类自动继承父分类类型
- **拖拽排序**：支持分类顺序调整
- **类型限制**：顶级分类不能移动，子分类只能在同类型内移动

### 账户功能

- **存量类账户**（资产/负债）

  - 余额更新功能
  - 历史余额趋势
  - 投资收益计算
  - 负债偿还跟踪

- **流量类账户**（收入/支出）
  - 交易记录管理
  - 现金流分析
  - 预算对比
  - 分类统计

### 多币种支持

- **账户货币**：每个账户独立设置货币
- **货币锁定**：账户有记录后不能更改货币
- **汇率转换**：自动转换为本位币显示
- **货币标签**：直观的货币标识和颜色编码

## 💰 交易管理系统

### 交易类型

- **收入交易**：工资、奖金、投资收益等
- **支出交易**：日常消费、账单支付等
- **余额调整**：存量账户的余额更新

### 交易功能

- **快速记录**：简化的交易录入界面
- **批量操作**：支持批量交易导入
- **标签系统**：灵活的交易标签管理
- **搜索过滤**：多维度交易查询

### 数据验证

- **类型匹配验证**：确保交易类型与账户类型匹配
- **金额验证**：防止异常金额录入
- **日期验证**：合理的日期范围检查
- **重复检查**：防止重复交易录入

## 💱 多币种汇率系统

### 货币管理

- **全球货币支持**：支持主要国际货币
- **自定义货币**：用户可创建自定义货币
- **货币激活**：管理用户可用货币列表
- **货币排序**：自定义货币显示顺序

### 汇率功能

- **手工录入汇率**：用户自定义汇率设置
- **历史汇率**：支持不同日期的汇率记录
- **汇率提醒**：缺失汇率的智能提醒
- **批量设置**：快速设置多个货币对汇率

### 转换计算

- **实时转换**：所有金额自动转换为本位币
- **精度保证**：使用Decimal类型确保计算精度
- **汇率缓存**：优化汇率查询性能
- **错误处理**：汇率缺失时的友好提示

## 📊 财务报表系统

### 资产负债表

- **时点报表**：反映特定时点的财务状况
- **资产分类**：流动资产、固定资产等
- **负债分类**：流动负债、长期负债等
- **净资产计算**：资产减负债的净值

### 现金流量表

- **期间报表**：反映特定期间的现金流动
- **经营活动**：日常收支现金流
- **投资活动**：投资相关现金流
- **筹资活动**：借贷相关现金流
- **净现金流**：各活动现金流汇总

### 报表特性

- **时间范围选择**：灵活的时间段设置
- **多币种支持**：统一转换为本位币显示
- **图表展示**：直观的数据可视化
- **导出功能**：支持PDF/Excel格式导出

## ⚙️ 用户设置系统

### 个人资料

- **基本信息**：邮箱、昵称等个人信息
- **头像管理**：用户头像上传和管理
- **密码修改**：安全的密码更改功能
- **账户统计**：用户数据概览信息

### 系统偏好

- **主题设置**：明亮、暗黑、系统主题
- **语言设置**：中文、英文双语支持
- **日期格式**：自定义日期显示格式
- **本位币设置**：主要货币选择和修改

### 货币管理

- **可用货币**：管理用户可使用的货币
- **汇率设置**：自定义汇率管理
- **货币排序**：调整货币显示顺序
- **货币激活**：启用/禁用特定货币

### 标签管理

- **标签创建**：自定义交易标签
- **颜色设置**：标签颜色个性化
- **标签排序**：调整标签显示顺序
- **批量操作**：标签的批量管理

## 🔥 FIRE功能系统

### FIRE概念

- **Financial Independence, Retire Early**：财务独立，提前退休
- **安全提取率**：可持续的资产提取比例
- **目标设定**：个性化的FIRE目标规划

### 核心指标

- **净资产追踪**：实时净资产监控
- **储蓄率计算**：收入储蓄比例分析
- **FIRE进度**：目标达成进度显示
- **时间预测**：预计达成FIRE的时间

### 可视化分析

- **趋势图表**：净资产增长趋势
- **目标对比**：当前状况与目标对比
- **情景分析**：不同储蓄率的影响
- **退休规划**：退休后的财务规划

## 🎨 用户界面设计

### 设计原则

- **响应式设计**：完美适配桌面端和移动端
- **现代化界面**：简洁美观的用户界面
- **直观操作**：用户友好的交互设计
- **无障碍支持**：符合无障碍设计标准

### 主题系统

- **明亮主题**：适合日间使用的明亮界面
- **暗黑主题**：适合夜间使用的暗色界面
- **系统主题**：跟随系统主题自动切换
- **主题切换**：实时主题切换无需刷新

### 国际化支持

- **多语言**：支持中文和英文
- **本地化**：日期、数字格式本地化
- **动态切换**：实时语言切换
- **翻译完整性**：全面的界面翻译

### 视觉反馈

- **颜色编码**：不同账户类型的颜色区分
- **状态指示**：清晰的状态和进度指示
- **动画效果**：流畅的过渡动画
- **加载状态**：友好的加载和骨架屏

## 📱 移动端适配

### 响应式布局

- **移动端优先**：优先考虑移动端体验
- **触摸友好**：适合触摸操作的界面元素
- **手势支持**：支持常用手势操作
- **屏幕适配**：适配各种屏幕尺寸

### 移动端特性

- **抽屉式导航**：移动端侧边栏设计
- **底部导航**：便于单手操作的导航
- **快速操作**：移动端优化的快速操作
- **离线支持**：基础的离线功能支持

## 🔧 系统架构

### 前端架构

- **组件化设计**：模块化的组件架构
- **状态管理**：Context API状态管理
- **路由管理**：Next.js App Router
- **性能优化**：代码分割和懒加载

### 后端架构

- **API设计**：RESTful API设计
- **数据库设计**：关系型数据库设计
- **安全机制**：完善的安全防护
- **错误处理**：统一的错误处理机制

### 部署架构

- **容器化部署**：Docker容器化
- **环境配置**：多环境配置管理
- **监控日志**：系统监控和日志记录
- **备份恢复**：数据备份和恢复机制

## 📈 性能优化

### 前端优化

- **代码分割**：按需加载减少初始包大小
- **图片优化**：响应式图片和懒加载
- **缓存策略**：合理的缓存策略
- **渲染优化**：React性能优化技巧

### 后端优化

- **数据库优化**：索引优化和查询优化
- **API优化**：减少不必要的API调用
- **缓存机制**：Redis缓存加速
- **并发处理**：高并发请求处理

## 🛡️ 安全机制

### 数据安全

- **密码加密**：bcryptjs密码哈希
- **JWT认证**：安全的令牌认证
- **数据验证**：Zod数据验证
- **SQL注入防护**：Prisma ORM防护

### 隐私保护

- **数据隔离**：用户数据完全隔离
- **敏感信息保护**：敏感数据加密存储
- **访问控制**：严格的访问权限控制
- **审计日志**：操作审计和日志记录

## 🚀 未来规划

### 短期目标

- **性能优化**：进一步提升系统性能
- **功能完善**：补充缺失的功能模块
- **用户体验**：优化用户交互体验
- **测试覆盖**：提高测试覆盖率

### 中期目标

- **高级分析**：更多财务分析功能
- **自动化**：自动化数据处理
- **集成功能**：第三方服务集成
- **移动应用**：原生移动应用开发

### 长期目标

- **AI功能**：智能财务分析和建议
- **社区功能**：用户社区和分享
- **企业版本**：面向企业的版本
- **国际化**：支持更多语言和地区

---

**文档版本**：v1.0  
**最后更新**：2025年6月18日  
**维护团队**：Flow Balance开发团队
