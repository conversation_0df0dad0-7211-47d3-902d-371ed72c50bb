# 账户管理功能使用指南

## 🎯 功能概述

Flow
Balance 提供了完整的账户管理功能，包括账户删除、余额记录编辑、批量操作等。支持存量类账户（资产/负债）和流量类账户（收入/支出）的全面管理，确保用户可以灵活管理账户数据。

## ✅ 新增功能

### 1. 存量账户余额记录编辑

- **恢复编辑功能**：存量账户的余额变化记录现在支持编辑
- **智能编辑界面**：使用专门的余额更新模态框进行编辑
- **数据完整性**：编辑时保持数据的一致性和准确性

### 2. 批量操作功能

- **批量删除**：选择多条记录进行批量删除
- **智能选择**：支持全选/取消全选功能
- **批量编辑**：功能已开发但暂时隐藏，待后续完善

### 3. 账户删除问题解决

- **核心问题**：存量账户因为有余额更新记录而无法删除
- **解决方案**：提供多种删除余额记录的方式，让用户可以清理数据后删除账户

## 📍 删除功能位置

### 1. 侧边栏账户树中删除

**操作步骤：**

1. 在左侧账户树中找到要删除的账户
2. 将鼠标悬停在账户行上，会看到右侧的"更多操作"按钮（三个点图标）
3. 点击"更多操作"按钮，打开上下文菜单
4. 在菜单底部找到红色的"删除"选项
5. 点击"删除"，确认删除操作

**提示：** "更多操作"按钮现在默认有60%透明度，悬停时变为100%不透明，更容易发现。

### 2. 账户详情页面中删除

**操作步骤：**

1. 点击账户名称进入账户详情页面
2. 在页面右上角的操作按钮区域，找到红色的"删除账户"按钮
3. 点击"删除账户"按钮
4. 在确认对话框中确认删除操作

### 3. 余额记录管理（存量账户专用）

**单条记录编辑：**

1. 进入存量账户详情页面
2. 在"余额变化记录"列表中，点击记录右侧的编辑按钮（铅笔图标）
3. 在弹出的编辑模态框中修改金额、日期、备注等信息
4. 点击"保存修改"完成编辑

**单条记录删除：**

1. 进入存量账户详情页面
2. 在"余额变化记录"列表中，每条余额调整记录都有删除按钮
3. 点击记录右侧的删除按钮（垃圾桶图标）
4. 确认删除该条余额记录

**批量删除：**

1. 进入账户详情页面（存量或流量账户）
2. 勾选要删除的记录（可使用全选功能）
3. 点击"批量删除"按钮
4. 确认删除操作

**批量清空记录：**

1. 进入存量账户详情页面
2. 在"余额变化记录"标题栏右侧，点击"清空记录"按钮
3. 确认清空所有余额调整记录

## 🔧 存量账户删除逻辑

### 普通删除流程

对于没有交易记录的账户，删除过程很简单：

1. 点击删除按钮
2. 确认删除
3. 账户被成功删除

### 存量账户特殊处理

对于有余额调整记录的存量账户（资产/负债），现在提供多种解决方案：

#### 方案1：先清理记录，再删除账户

1. **单条删除**：在账户详情页面逐条删除余额记录
2. **批量清空**：使用"清空记录"按钮一次性删除所有余额记录
3. **删除账户**：清空记录后，账户删除功能正常可用

#### 方案2：智能删除流程（原有功能）

1. 直接点击删除账户按钮
2. 系统检测到余额调整记录，显示详细错误信息
3. 系统询问是否清空余额历史记录
4. 确认清空后，系统删除所有余额调整记录
5. 系统询问是否继续删除账户
6. 确认后账户被成功删除

#### 情况：有普通交易记录

1. 点击删除按钮
2. 系统显示详细错误信息，说明有多少条普通交易和余额调整记录
3. 用户需要先手动删除普通交易记录
4. 然后可以使用上述任一方案清空余额历史并删除账户

## 💡 用户体验优化

### 1. 明确的错误提示

- **详细信息**：系统会准确告知有多少条不同类型的交易记录
- **操作指导**：提供清晰的下一步操作建议
- **风险提醒**：明确说明删除操作的不可逆性

### 2. 智能删除流程

- **自动检测**：系统自动识别账户类型和记录类型
- **分步处理**：将复杂的删除过程分解为简单的确认步骤
- **安全确认**：每个关键步骤都需要用户明确确认

### 3. 多入口设计

- **侧边栏快速操作**：适合快速管理多个账户
- **详情页面操作**：适合在查看账户详情时进行删除

## 🚨 注意事项

### 1. 数据安全

- **不可撤销**：账户删除操作无法撤销
- **级联删除**：删除账户会同时删除所有相关的交易记录
- **备份建议**：重要数据建议在删除前先导出备份

### 2. 存量账户特殊性

- **余额记录**：存量账户的余额调整记录需要特殊处理
- **业务逻辑**：系统区分普通交易和余额调整，提供不同的处理方式
- **数据完整性**：确保删除过程不会破坏数据的完整性

### 3. 操作建议

- **先清理交易**：建议先删除不需要的交易记录，再删除账户
- **分类整理**：删除前可以考虑将有用的交易移动到其他账户
- **定期维护**：定期清理不需要的账户，保持系统整洁

## 🔄 删除流程图

```
开始删除账户
    ↓
检查账户类型
    ↓
┌─────────────────┬─────────────────┐
│   流量账户      │   存量账户      │
│ (收入/支出)     │ (资产/负债)     │
└─────────────────┴─────────────────┘
    ↓                     ↓
检查交易记录          检查交易记录
    ↓                     ↓
┌─────────┬─────────┐ ┌─────────┬─────────┐
│ 无记录  │ 有记录  │ │ 无记录  │ 有记录  │
└─────────┴─────────┘ └─────────┴─────────┘
    ↓         ↓           ↓         ↓
 直接删除   提示错误    直接删除   智能处理
                                    ↓
                              ┌─────────────┐
                              │ 分析记录类型 │
                              └─────────────┘
                                    ↓
                          ┌─────────┬─────────┐
                          │ 仅余额  │ 有普通  │
                          │ 调整    │ 交易    │
                          └─────────┴─────────┘
                                ↓         ↓
                           提供清空选项  要求先删除
                                ↓      普通交易
                           清空余额记录
                                ↓
                           继续删除账户
```

## 📞 技术支持

如果在使用删除功能时遇到问题：

1. 检查账户是否有未处理的交易记录
2. 确认网络连接正常
3. 刷新页面后重试
4. 查看浏览器控制台是否有错误信息

通过这个完整的删除功能，用户可以安全、便捷地管理账户，同时系统确保了数据的完整性和操作的安全性。
