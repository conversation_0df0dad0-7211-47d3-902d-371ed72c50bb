# Dashboard 计算逻辑修复计划

## 发现的问题

### 1. 流量类账户计算逻辑错误

**问题**: 当前流量类账户（收入/支出）累计了所有历史交易，这是不正确的。
**正确逻辑**: 流量类账户应该按期间计算，例如：

- 仪表板显示当月或近期的收入/支出
- 图表显示每月的收入/支出流量
- 不应该累计所有历史数据

### 2. 仪表板数据混淆

**问题**: 仪表板在计算汇总数据时，可能混淆了存量和流量的概念。 **正确逻辑**:

- 净资产 = 资产余额 - 负债余额（存量数据，时点概念）
- 现金流 = 期间收入 - 期间支出（流量数据，期间概念）

### 3. API 端点计算不一致

**问题**: 不同API端点可能使用了不同的计算逻辑。 **需要检查的端点**:

- `/api/dashboard/summary` - 仪表板汇总
- `/api/dashboard/charts` - 图表数据
- `/api/accounts/balances` - 账户余额

## 修复方案

### 阶段1: 修复流量类账户计算逻辑

#### 1.1 修改 `calculateAccountBalance` 函数

**文件**: `src/lib/account-balance.ts` **修改内容**:

- 为流量类账户添加期间参数
- 默认计算当前月份的流量
- 提供选项计算指定期间的流量

#### 1.2 修改 `/api/accounts/balances` 端点

**文件**: `src/app/api/accounts/balances/route.ts` **修改内容**:

- 流量类账户使用当前月份的交易计算
- 确保存量类账户使用最新余额

### 阶段2: 修复仪表板汇总计算

#### 2.1 修改 `/api/dashboard/summary` 端点

**文件**: `src/app/api/dashboard/summary/route.ts` **修改内容**:

- 净资产计算只使用存量类账户
- 现金流计算使用指定期间的流量类账户数据
- 分离存量和流量的计算逻辑

#### 2.2 修改 `/api/dashboard/charts` 端点

**文件**: `src/app/api/dashboard/charts/route.ts` **修改内容**:

- 确保净资产图表只使用存量类账户的月末余额
- 确保现金流图表使用每月的流量数据

### 阶段3: 修复前端显示逻辑

#### 3.1 修改仪表板组件

**文件**: `src/components/dashboard/DashboardContent.tsx` **修改内容**:

- 确保正确区分存量和流量数据的显示
- 修复汇总卡片的计算逻辑

#### 3.2 修改智能账户汇总组件

**文件**: `src/components/dashboard/SmartAccountSummary.tsx` **修改内容**:

- 流量数据显示期间选择器
- 正确计算和显示期间数据

## 实施步骤

1. **第一步**: 修复核心计算函数
2. **第二步**: 修复API端点
3. **第三步**: 修复前端组件
4. **第四步**: 全面测试验证

## 测试验证

### 测试用例

1. **存量类账户测试**:

   - 验证资产账户余额计算
   - 验证负债账户余额计算
   - 验证净资产计算

2. **流量类账户测试**:

   - 验证当月收入计算
   - 验证当月支出计算
   - 验证期间现金流计算

3. **仪表板显示测试**:
   - 验证汇总卡片数值
   - 验证图表数据
   - 验证多币种转换

### 预期结果

- 存量类账户显示当前时点余额
- 流量类账户显示期间累计金额
- 仪表板数据逻辑清晰，符合财务概念

## 已完成的修复

### ✅ 阶段1: 修复核心计算函数

#### 1.1 修改 `CalculationOptions` 接口

**文件**: `src/lib/account-balance.ts` **修改内容**:

- 添加了 `periodStart` 和 `periodEnd` 参数用于期间计算
- 添加了 `usePeriodCalculation` 参数控制是否使用期间计算

#### 1.2 新增 `calculateFlowAccountBalance` 函数

**文件**: `src/lib/account-balance.ts` **修改内容**:

- 专门处理流量类账户的期间计算
- 默认计算当前月份的流量
- 支持自定义期间计算
- 正确区分收入和支出交易类型

#### 1.3 修改 `calculateAccountBalance` 函数

**文件**: `src/lib/account-balance.ts` **修改内容**:

- 正确区分存量类账户和流量类账户
- 存量类账户使用 `calculateStockAccountBalance`
- 流量类账户使用 `calculateFlowAccountBalance`
- 保留向后兼容的兜底逻辑

### ✅ 阶段2: 修复API端点

#### 2.1 修改 `/api/accounts/balances` 端点

**文件**: `src/app/api/accounts/balances/route.ts` **修改内容**:

- 移除了旧的 `calculateCurrentMonthFlow` 函数
- 流量类账户使用统一的 `calculateAccountBalance` 函数
- 添加期间参数，默认计算当前月份

#### 2.2 修改 `/api/dashboard/summary` 端点

**文件**: `src/app/api/dashboard/summary/route.ts` **修改内容**:

- 分离存量类账户和流量类账户
- 净资产计算只使用存量类账户
- 流量类账户使用期间计算（当前月份）
- 正确区分存量和流量数据的显示

### ✅ 测试验证

#### 测试结果

1. **存量类账户测试** ✅:

   - 资产账户正确使用最新余额调整
   - 负债账户正确计算当前余额
   - 净资产计算正确（¥178,349.74）

2. **流量类账户测试** ✅:

   - 收入账户正确计算当月收入（¥50,000.00）
   - 支出账户正确计算当月支出（¥500.00 CNY + USD支出）
   - 期间现金流计算正确（¥49,500.00）

3. **计算逻辑验证** ✅:
   - ✓ 存量类账户使用最新余额调整 + 后续交易
   - ✓ 流量类账户使用期间内交易累计
   - ✓ 净资产只包含存量类账户
   - ✓ 现金流只包含流量类账户的期间数据

### 🔍 发现的问题和建议

1. **数据完整性**:

   - 发现账户 "test2" 缺少余额调整，建议添加初始余额
   - 流量类账户数据正常，没有异常的余额调整交易

2. **汇率转换**:
   - 当前测试中USD和JPY的支出没有计入本位币汇总
   - 建议检查汇率设置是否完整

## 下一步工作

### 待完成项目

1. **前端组件验证**: 检查仪表板前端组件是否正确显示修复后的数值
2. **图表数据验证**: 确认图表显示的历史数据是否正确
3. **多币种支持**: 完善汇率转换逻辑
4. **用户界面优化**: 确保UI正确区分存量和流量数据的展示

### 建议的后续测试

1. 在浏览器中验证仪表板显示
2. 检查图表数据的准确性
3. 测试多币种账户的计算
4. 验证期间选择功能
