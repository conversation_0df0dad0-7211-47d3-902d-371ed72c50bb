# 货币代码重复验证功能

## 📋 概述

本文档描述了为防止同一用户选择多个相同货币代码的货币而实施的验证机制。

## 🎯 问题描述

在用户货币设置中，可能存在以下情况：

- 全局货币和用户自定义货币具有相同的货币代码（如 CNY）
- 用户可能会意外选择多个相同代码的不同货币
- 这会导致数据不一致和用户体验混乱

## ✅ 解决方案

### 1. 批量货币设置验证 (`PUT /api/user/currencies`)

**新增验证逻辑：**

1. **重复代码检测**

   ```typescript
   const uniqueCodes = new Set(currencyCodes)
   if (uniqueCodes.size !== currencyCodes.length) {
     // 检测到重复的货币代码
   }
   ```

2. **智能货币选择**

   - 为每个货币代码选择优先级最高的货币
   - 优先级：用户自定义货币 > 全局货币
   - 确保每个代码只选择一个货币

3. **冲突检测**
   - 防止同一代码被多次选择
   - 提供清晰的错误信息

### 2. 单个货币添加验证 (`POST /api/user/currencies`)

**新增验证逻辑：**

1. **同代码货币检测**

   ```typescript
   const existingCurrenciesWithSameCode = await prisma.userCurrency.findMany({
     where: {
       userId: user.id,
       isActive: true,
       currency: { code: currencyCode },
     },
   })
   ```

2. **冲突处理**
   - 如果是同一货币：提示已存在
   - 如果是不同货币但代码相同：阻止添加并提示冲突

### 3. 自定义货币创建验证 (`POST /api/currencies/custom`)

**新增验证逻辑：**

1. **创建前检查**
   - 检查用户是否已选择相同代码的其他货币
   - 防止创建会导致冲突的自定义货币

## 🧪 测试验证

### 测试场景

1. **正常场景**

   - ✅ 选择不同代码的货币列表
   - ✅ 添加新的有效货币

2. **错误场景**
   - ❌ 货币代码列表中包含重复项
   - ❌ 包含无效的货币代码
   - ❌ 尝试添加已存在的货币
   - ❌ 尝试添加相同代码的不同货币

### 测试结果

```bash
🧪 测试批量更新货币设置...
   输入代码: USD, EUR, USD
   ❌ 货币代码列表中存在重复项: USD

🧪 测试添加单个货币 CNY...
   ❌ 您已选择了货币代码为 CNY 的其他货币，同一货币代码只能选择一次
```

## 📝 错误信息

### 中文错误信息

- `货币代码列表中存在重复项: {codes}`
- `您已选择了货币代码为 {code} 的其他货币，同一货币代码只能选择一次`
- `货币代码 {code} 存在多个可选项，请确保每个货币代码只选择一次`

## 🔧 技术实现

### 数据库约束

现有约束已足够：

- `Currency` 模型：`@@unique([createdBy, code])` - 防止同一用户创建相同代码的货币
- `UserCurrency` 模型：`@@unique([userId, currencyId])` - 防止同一用户选择同一货币多次

### API 层验证

在应用层添加业务逻辑验证，确保：

- 同一用户不能选择多个相同代码的货币
- 提供清晰的错误信息和用户指导
- 智能选择优先级最高的货币

## 🎯 影响范围

### 修改的文件

1. `src/app/api/user/currencies/route.ts`

   - 批量设置货币验证逻辑
   - 单个添加货币验证逻辑

2. `src/app/api/currencies/custom/route.ts`
   - 自定义货币创建验证逻辑

### 向后兼容性

- ✅ 完全向后兼容
- ✅ 不影响现有数据
- ✅ 只在新操作时进行验证

## 🚀 部署建议

1. **测试验证**

   - 运行测试脚本验证功能
   - 检查现有用户数据是否有冲突

2. **监控**

   - 监控API错误率
   - 关注用户反馈

3. **文档更新**
   - 更新API文档
   - 更新用户指南

## 📊 预期效果

- 🎯 **数据一致性**：确保用户货币设置的唯一性
- 🛡️ **错误预防**：防止用户意外选择冲突的货币
- 📱 **用户体验**：提供清晰的错误信息和指导
- 🔧 **系统稳定性**：减少因货币代码冲突导致的问题
