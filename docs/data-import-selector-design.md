# 🎨 数据导入选择器设计文档

## 📋 设计概述

### 核心功能

- **可视化数据统计**: 将原来的简单文本统计升级为美观的卡片式展示
- **选择性导入**: 用户可以选择需要导入的数据类型
- **智能依赖管理**: 自动处理数据类型之间的依赖关系
- **美观的UI设计**: 现代化界面，支持暗色模式

### 设计理念

1. **用户友好**: 清晰的视觉层次和直观的交互
2. **信息丰富**: 每个数据类型显示详细信息和统计
3. **智能化**: 自动处理依赖关系，避免用户错误操作
4. **响应式**: 适配不同屏幕尺寸

## 🎯 UI设计特点

### 1. **数据类型卡片**

```
┌─────────────────────────────────┐
│ [✓] 📁 分类 *                   │
│     11 条记录                   │
│     账户分类和层级结构           │
│     [必需]                      │
└─────────────────────────────────┘
```

**卡片元素**:

- **选择框**: 右上角的复选框
- **图标**: 彩色圆形背景的emoji图标
- **标题**: 数据类型名称 + 必需标识(\*)
- **数量**: 大字体显示记录数量
- **描述**: 简短的功能说明
- **依赖关系**: 显示依赖的其他数据类型
- **必需标识**: 红色标签标识必需项

### 2. **颜色系统**

| 数据类型 | 颜色 | 图标 | 说明         |
| -------- | ---- | ---- | ------------ |
| 分类     | 紫色 | 📁   | 基础分类结构 |
| 账户     | 蓝色 | 🏦   | 财务账户     |
| 交易     | 绿色 | 💳   | 交易记录     |
| 标签     | 绿色 | 🏷️   | 标签系统     |
| 货币     | 黄色 | 💱   | 货币设置     |
| 汇率     | 橙色 | 📈   | 汇率数据     |
| 模板     | 靛蓝 | 📋   | 交易模板     |
| 定期交易 | 青色 | 🔄   | 定期设置     |
| 贷款合约 | 红色 | 🏠   | 贷款信息     |
| 还款记录 | 粉色 | 💰   | 还款数据     |

### 3. **布局结构**

```
┌─────────────────────────────────────────────────────────┐
│ 选择导入数据                              [✓] 全选      │
├─────────────────────────────────────────────────────────┤
│ 已选择 10/10 项                          共 1,126 条记录 │
├─────────────────────────────────────────────────────────┤
│ ┌─────────┐ ┌─────────┐ ┌─────────┐                     │
│ │ 分类    │ │ 账户    │ │ 交易    │                     │
│ │ 11      │ │ 23      │ │ 652     │                     │
│ └─────────┘ └─────────┘ └─────────┘                     │
│ ┌─────────┐ ┌─────────┐ ┌─────────┐                     │
│ │ 标签    │ │ 货币    │ │ 汇率    │                     │
│ │ 4       │ │ 6       │ │ 31      │                     │
│ └─────────┘ └─────────┘ └─────────┘                     │
│ ┌─────────┐ ┌─────────┐ ┌─────────┐                     │
│ │ 模板    │ │ 定期    │ │ 贷款    │                     │
│ │ 0       │ │ 2       │ │ 2       │                     │
│ └─────────┘ └─────────┘ └─────────┘                     │
│ ┌─────────┐                                             │
│ │ 还款    │                                             │
│ │ 396     │                                             │
│ └─────────┘                                             │
├─────────────────────────────────────────────────────────┤
│ 💡 数据类型之间存在依赖关系...                          │
│ • 账户依赖分类，交易模板和定期交易依赖账户              │
│ • 还款记录依赖贷款合约                                  │
│ • 汇率依赖货币设置                                      │
└─────────────────────────────────────────────────────────┘
```

## 🔧 技术实现

### 1. **类型定义扩展**

```typescript
// 新增选择性导入配置
export interface ImportDataTypeSelection {
  categories?: boolean
  accounts?: boolean
  transactions?: boolean
  tags?: boolean
  currencies?: boolean
  exchangeRates?: boolean
  transactionTemplates?: boolean
  recurringTransactions?: boolean
  loanContracts?: boolean
  loanPayments?: boolean
}

// 数据类型信息
export interface DataTypeInfo {
  key: keyof ImportDataTypeSelection
  name: string
  icon: string
  count: number
  enabled: boolean
  required?: boolean
  dependsOn?: Array<keyof ImportDataTypeSelection>
  description?: string
  color: string
}
```

### 2. **依赖关系管理**

```typescript
const dependencies = {
  accounts: ['categories'], // 账户依赖分类
  transactions: ['accounts', 'tags'], // 交易依赖账户和标签
  transactionTemplates: ['accounts', 'tags'],
  recurringTransactions: ['accounts', 'tags'],
  loanContracts: ['accounts'],
  loanPayments: ['loanContracts'], // 还款记录依赖贷款合约
  exchangeRates: ['currencies'], // 汇率依赖货币
}
```

### 3. **智能选择逻辑**

- **选择时**: 自动选择所有依赖项
- **取消时**: 自动取消所有依赖此项的项目
- **必需项**: 分类、账户、货币不能取消选择

## 📊 用户体验改进

### 1. **原来的体验**

```
数据统计:
分类: 11        账户: 23        交易: 652
标签: 4         货币: 6         汇率: 31
交易模板: 0     定期交易: 2     贷款合约: 2
还款记录: 396

[✓] 跳过重复数据
[✓] 覆盖现有数据
[✓] 创建缺失的货币
```

### 2. **改进后的体验**

- **可视化统计**: 美观的卡片式展示
- **选择性导入**: 可以选择需要的数据类型
- **智能提示**: 显示依赖关系和建议
- **实时反馈**: 显示选中项目和总记录数

### 3. **交互改进**

- **全选/取消全选**: 一键操作
- **依赖自动处理**: 避免用户错误操作
- **视觉反馈**: 选中状态清晰可见
- **响应式布局**: 适配不同设备

## 🎨 视觉设计

### 1. **卡片状态**

- **未选中**: 灰色边框，白色背景
- **选中**: 蓝色边框，蓝色背景
- **必需项**: 红色标识，不可取消
- **禁用**: 半透明显示

### 2. **颜色主题**

- **主色调**: 蓝色系 (选中状态)
- **辅助色**: 各数据类型的特定颜色
- **中性色**: 灰色系 (未选中状态)
- **强调色**: 红色 (必需项标识)

### 3. **暗色模式支持**

- 自动适配系统主题
- 保持良好的对比度
- 颜色在暗色模式下的适配

## 🚀 实施效果

### 1. **功能完整性**

- ✅ 可选择导入数据类型
- ✅ 智能依赖关系处理
- ✅ 美观的UI界面
- ✅ 响应式布局
- ✅ 国际化支持

### 2. **用户体验提升**

- 📈 更直观的数据展示
- 🎯 精确的导入控制
- 💡 智能的操作提示
- 🎨 现代化的界面设计

### 3. **技术优势**

- 🔧 模块化组件设计
- 📱 响应式布局
- 🌙 暗色模式支持
- 🌍 完整的国际化

这个设计将数据导入体验从简单的文本列表升级为现代化的可视化选择界面，大大提升了用户体验和操作便利性。
