# BalanceSheetCard 组件优化文档

## 优化概述

本次优化对 `BalanceSheetCard` 组件进行了重大改进，通过集成 `UserDataContext`
中的分类树数据，实现了更清晰的层级汇总展示。

## 主要改进

### 1. 集成 UserDataContext

- 从 `UserDataContext` 获取完整的 `categories` 和 `accounts` 数据
- 利用已有的分类树结构，避免重复的 API 调用
- 保持数据的一致性和实时性

### 2. 层级分类树构建

- 新增 `enrichedCategoryTree` 计算属性，构建完整的分类层级结构
- 支持多级分类的嵌套显示
- 自动汇总子分类的余额到父分类

### 3. 新的渲染逻辑

- 新增 `renderHierarchicalCategories` 函数，支持递归渲染分类树
- 保留原有的 `renderCategorySection` 函数作为后备方案
- 智能切换：优先使用层级展示，数据不可用时回退到原有展示

### 4. 视觉改进

- 通过缩进显示分类层级关系
- 顶级分类显示账户类型标签（资产、负债、收入、支出）
- 保持原有的货币转换和显示逻辑

## 技术实现

### 核心数据结构

```typescript
interface CategoryWithAccounts {
  id: string
  name: string
  type: 'ASSET' | 'LIABILITY' | 'INCOME' | 'EXPENSE'
  parentId?: string | null
  order: number
  children?: CategoryWithAccounts[]
  accounts: AccountInfo[]
  totalByCurrency: Record<string, number>
  totalInBaseCurrency?: number
}
```

### 分类树构建算法

1. **初始化分类映射**：为每个分类创建节点
2. **构建层级关系**：根据 `parentId` 建立父子关系
3. **填充账户数据**：将 API 返回的账户数据分配到对应分类
4. **递归汇总余额**：从叶子节点向上汇总余额到父分类
5. **排序优化**：按 `order` 字段排序分类和账户

### 渲染逻辑

```typescript
const renderHierarchicalCategories = (
  categories: CategoryWithAccounts[],
  level: number = 0
) => {
  return categories.map(category => (
    <div key={category.id} className="mb-4">
      {/* 分类标题 - 带层级缩进 */}
      <div style={{ paddingLeft: `${level * 16}px` }}>
        {category.name}
        {/* 顶级分类显示类型标签 */}
      </div>

      {/* 账户列表 */}
      {category.accounts.length > 0 && (
        // 显示账户和余额
      )}

      {/* 递归渲染子分类 */}
      {category.children && category.children.length > 0 && (
        renderHierarchicalCategories(category.children, level + 1)
      )}
    </div>
  ))
}
```

## 优化效果

### 1. 更清晰的层级展示

- 用户可以清楚地看到分类的层级关系
- 父分类自动汇总子分类的余额
- 支持任意深度的分类嵌套

### 2. 更好的数据一致性

- 利用 UserDataContext 的统一数据源
- 避免数据不同步的问题
- 减少不必要的 API 调用

### 3. 更强的扩展性

- 保留原有渲染逻辑作为后备
- 新的层级渲染逻辑可以轻松扩展
- 支持未来的功能增强

### 4. 更好的用户体验

- 视觉层次更加清晰
- 信息组织更加合理
- 保持原有的所有功能

## 兼容性

- 完全向后兼容，不影响现有功能
- 当 UserDataContext 数据不可用时，自动回退到原有展示方式
- 保持所有原有的货币转换和国际化功能

## 未来改进方向

1. **可折叠分类树**：添加展开/折叠功能
2. **分类汇总统计**：在父分类显示更详细的统计信息
3. **交互式导航**：点击分类名称跳转到分类详情页
4. **自定义排序**：允许用户自定义分类显示顺序
5. **搜索过滤**：添加分类和账户的搜索功能

## 第二轮优化：分类汇总和样式改进

### 问题识别

在第一轮优化后，用户反馈了以下问题：

1. 分类汇总节点缺少本币汇总金额显示
2. 右侧金额显示没有正确对齐
3. 需要为分类汇总使用不同的视觉样式

### 解决方案

#### 1. 完善本币汇总计算

```typescript
// 确保每个分类都有本币汇总金额
if (category.totalInBaseCurrency === undefined || category.totalInBaseCurrency === 0) {
  if (category.accounts.length > 0) {
    category.totalInBaseCurrency = category.accounts.reduce((sum, account) => {
      return sum + (account.balanceInBaseCurrency || 0)
    }, 0)
  } else {
    category.totalInBaseCurrency = 0
  }
}
```

#### 2. 分类汇总特殊样式

为分类汇总金额添加了带边框的特殊样式：

- 顶级分类：蓝色背景和边框
- 子分类：灰色背景和边框
- 使用 `inline-block` 和 `px-2 py-1 rounded` 创建标签效果

#### 3. 右侧对齐优化

- 使用 `text-right min-w-0 flex-shrink-0` 确保右侧内容正确对齐
- 为账户名称添加 `flex-1 min-w-0 pr-2` 确保左侧内容不会挤压右侧
- 使用 `items-start` 确保多行内容顶部对齐

### 视觉效果改进

#### 分类汇总显示

```
资产 [资产]                    ¥1,111.10  <- 带边框的特殊样式
  股票                        ¥1,111.10  <- 子分类汇总
    JPY                       ¥22,222.00
                              ≈ ¥1,111.10
    • test3                   ¥22,222.00  <- 右侧完美对齐
                              ≈ ¥1,111.10
```

#### 样式特点

1. **分类汇总**：带边框的标签样式，视觉上突出显示
2. **币种小计**：灰色背景，清晰区分
3. **账户明细**：右侧金额完美对齐，支持多行显示

## 总结

本次优化成功地将 BalanceSheetCard 组件从简单的扁平展示升级为层级化的树状展示，并进一步完善了分类汇总和样式显示。主要成果包括：

1. **完整的层级汇总**：每个分类节点都显示本币汇总金额
2. **清晰的视觉层次**：通过不同样式区分分类汇总、币种小计和账户明细
3. **完美的对齐效果**：右侧金额显示整齐对齐，提升可读性
4. **保持向后兼容**：所有原有功能完整保留

通过合理利用现有的数据结构和上下文，实现了功能增强的同时保持了代码的简洁性和可维护性。
