# Flow Balance 着陆页截图指南

## 📸 截图需求概述

为了完善 Flow
Balance 着陆页的产品展示，需要您提供以下高质量的界面截图。这些截图将用于向潜在用户展示产品的实际外观和智能化功能。

## 🎯 截图规格要求

### 基本要求

- **分辨率**: 1920×1080 (桌面端) 或 375×812 (移动端)
- **格式**: PNG 格式（保持最佳清晰度）
- **质量**: 高清截图，确保文字清晰可读
- **内容**: 使用真实数据，避免空白状态
- **浏览器**: 建议使用 Chrome 浏览器截图

### 数据要求

- 账户应有合理的余额数据
- 交易记录应该丰富且真实
- 图表应显示有意义的数据
- 避免显示敏感的真实个人信息

## 📋 详细截图清单

### 1. 仪表板概览截图

**文件名**: `dashboard-overview.png` **存放位置**:
`public/images/screenshots/dashboard-overview.png` **页面**: `/dashboard` **要求**:

- 显示主要的财务概览卡片
- 包含图表和统计数据
- 显示账户余额信息
- 使用明亮主题
- 确保数据看起来真实和清晰

### 2. 财务报表截图

**文件名**: `financial-reports.png` **存放位置**: `public/images/screenshots/financial-reports.png`
**页面**: `/reports` **要求**:

- 显示资产负债表或现金流量表
- 包含图表可视化
- 显示多个时间段的数据
- 体现清晰的财务分析功能

### 3. FIRE 计算器截图

**文件名**: `fire-calculator.png` **存放位置**: `public/images/screenshots/fire-calculator.png`
**页面**: `/fire` **要求**:

- 显示 FIRE 计算界面
- 包含预测图表和计算结果
- 显示财务自由目标设定
- 体现智能的财务规划功能

### 4. 智能批量操作截图

**文件名**: `smart-paste.png` **存放位置**: `public/images/screenshots/smart-paste.png`
**页面**: 交易页面的智能粘贴功能 **要求**:

- 显示智能粘贴或批量编辑界面
- 表格中应有多行数据
- 显示验证状态和反馈
- 体现 Excel 式的操作体验

### 5. 明亮主题截图

**文件名**: `theme-light.png` **存放位置**: `public/images/screenshots/theme-light.png`
**页面**: 任意主要功能页面（建议账户管理或交易列表） **要求**:

- 使用明亮主题
- 界面应该清晰明亮
- 显示完整的页面布局
- 包含侧边栏和主要内容区域

### 6. 暗黑主题截图

**文件名**: `theme-dark.png` **存放位置**: `public/images/screenshots/theme-dark.png`
**页面**: 与明亮主题相同的页面 **要求**:

- 使用暗黑主题
- 与明亮主题截图内容相同
- 展示暗黑主题的优雅设计
- 确保文字在暗色背景下清晰可读

### 7. 中文界面截图

**文件名**: `interface-zh.png` **存放位置**: `public/images/screenshots/interface-zh.png`
**页面**: 设置页面或主要功能页面 **要求**:

- 界面语言设置为中文
- 显示完整的中文界面元素
- 包含菜单、按钮、标签等文本
- 体现完整的本地化支持

### 8. 英文界面截图

**文件名**: `interface-en.png` **存放位置**: `public/images/screenshots/interface-en.png`
**页面**: 与中文界面相同的页面 **要求**:

- 界面语言设置为英文
- 与中文界面截图内容相同
- 展示国际化的完整性
- 确保英文文本显示正确

### 9. 多币种管理截图

**文件名**: `multi-currency.png` **存放位置**: `public/images/screenshots/multi-currency.png`
**页面**: 账户管理或货币设置页面 **要求**:

- 显示多种货币的账户
- 包含货币标签和汇率信息
- 体现多币种管理功能
- 显示不同货币的余额

### 10. 移动端响应式截图

**文件名**: `mobile-responsive.png` **存放位置**: `public/images/screenshots/mobile-responsive.png`
**页面**: 主要功能页面的移动端视图 **要求**:

- 使用浏览器开发者工具模拟移动端
- 分辨率: 375×812 (iPhone X 尺寸)
- 显示移动端的导航和布局
- 体现响应式设计效果

## 🛠️ 截图操作步骤

### 桌面端截图

1. 打开 Chrome 浏览器
2. 访问 `http://localhost:3002` 并登录
3. 导航到指定页面
4. 按 F12 打开开发者工具
5. 设置浏览器窗口为 1920×1080
6. 按 F12 关闭开发者工具
7. 使用截图工具截取完整页面

### 移动端截图

1. 打开 Chrome 浏览器开发者工具
2. 点击设备模拟按钮
3. 选择 iPhone X (375×812)
4. 导航到指定页面
5. 截取移动端视图

### 主题切换

- 点击右上角的主题切换按钮
- 确认主题已完全切换
- 等待页面完全加载后截图

### 语言切换

- 点击右上角的语言切换按钮
- 确认界面语言已完全切换
- 等待翻译加载完成后截图

## 📁 文件组织

截图完成后，请确保文件按以下结构组织：

```
public/images/screenshots/
├── dashboard-overview.png
├── financial-reports.png
├── fire-calculator.png
├── smart-paste.png
├── theme-light.png
├── theme-dark.png
├── interface-zh.png
├── interface-en.png
├── multi-currency.png
└── mobile-responsive.png
```

## ✅ 质量检查清单

截图完成后，请检查：

- [ ] 所有文件名正确无误
- [ ] 图片分辨率符合要求
- [ ] 图片清晰，文字可读
- [ ] 数据内容真实合理
- [ ] 主题和语言设置正确
- [ ] 文件保存在正确位置
- [ ] PNG 格式，文件大小合理

## 🔄 替换占位符

截图准备好后：

1. 将截图文件放入 `public/images/screenshots/` 目录
2. 刷新着陆页 (`http://localhost:3002`)
3. 占位符将自动被真实截图替换
4. 检查所有截图是否正确显示

## 💡 提示

- 建议在截图前准备一些测试数据，让界面看起来更真实
- 可以使用浏览器的无痕模式避免扩展程序干扰
- 截图时确保页面完全加载，避免加载状态
- 如果某个功能还在开发中，可以先跳过对应截图
- 截图质量直接影响着陆页的专业度，请确保高质量

完成截图后，着陆页将展现出完整的产品界面，大大提升用户对产品的信心和兴趣！
