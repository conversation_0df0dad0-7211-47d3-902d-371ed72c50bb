# 恢复密钥功能改造实施计划

## 项目概述

为 Flow
Balance 个人财务管理系统添加恢复密钥功能，替代现有的邮件重置密码方案。用户注册时自动生成唯一恢复密钥，忘记密码时通过恢复密钥进行身份验证和密码重置。

## 功能需求

### 核心功能

1. **注册时生成恢复密钥**：用户注册完成后自动生成并展示恢复密钥
2. **恢复密钥展示页面**：要求用户确认已保存密钥
3. **忘记密码验证**：使用邮箱+恢复密钥进行身份验证
4. **密码重置**：验证通过后允许设置新密码
5. **安全设置管理**：查看、重新生成恢复密钥

### 技术要求

1. **国际化支持**：所有文本支持中英文切换
2. **主题适配**：支持明暗主题
3. **编码规范**：遵循项目现有编码规范
4. **安全性**：防止暴力破解，审计日志

## 实施计划

### 阶段一：数据库设计和迁移 (1-2小时)

- [x] 1.1 更新 Prisma Schema
- [x] 1.2 创建数据库迁移文件
- [x] 1.3 执行迁移

### 阶段二：后端API开发 (3-4小时)

- [x] 2.1 恢复密钥生成工具函数
- [x] 2.2 注册API更新（自动生成恢复密钥）
- [x] 2.3 恢复密钥验证API
- [x] 2.4 密码重置API（使用恢复密钥）
- [x] 2.5 恢复密钥管理API

### 阶段三：国际化文本 (1小时)

- [x] 3.1 添加中文文本
- [x] 3.2 添加英文文本
- [x] 3.3 更新类型定义

### 阶段四：前端组件开发 (4-5小时)

- [x] 4.1 恢复密钥展示组件
- [x] 4.2 忘记密码表单组件（使用恢复密钥）
- [x] 4.3 安全设置中的恢复密钥管理
- [x] 4.4 主题样式适配

### 阶段五：流程集成 (2-3小时)

- [x] 5.1 更新注册流程
- [x] 5.2 更新忘记密码流程
- [x] 5.3 更新安全设置页面

### 阶段六：测试和优化 (2小时)

- [x] 6.1 功能测试
- [x] 6.2 安全测试
- [x] 6.3 用户体验优化

## 技术规范

### 恢复密钥格式

```
格式：FB-XXXX-XXXX-XXXX-XXXX
- FB: Flow Balance 前缀
- 每段4位字符
- 使用字符集：23456789ABCDEFGHJKMNPQRSTUVWXYZ（排除易混淆字符）
- 总长度：19字符（包含连字符）
```

### 数据库设计

```prisma
model User {
  // 新增字段
  recoveryKey         String?   @unique
  recoveryKeyCreatedAt DateTime?
}
```

### API端点设计

```
POST /api/auth/generate-recovery-key    # 生成新恢复密钥
POST /api/auth/verify-recovery-key      # 验证恢复密钥
POST /api/auth/reset-password-with-key  # 使用恢复密钥重置密码
GET  /api/user/recovery-key             # 获取当前恢复密钥
```

### 组件结构

```
components/features/auth/
├── RecoveryKeyDisplay.tsx              # 恢复密钥展示页面
├── ForgotPasswordWithKey.tsx           # 使用恢复密钥的忘记密码表单
└── RecoveryKeyManagement.tsx           # 安全设置中的密钥管理

components/ui/
└── RecoveryKeyInput.tsx                # 恢复密钥输入组件
```

## 安全考虑

### 防护措施

1. **尝试限制**：每个IP每小时最多5次验证尝试
2. **时间窗口**：重置密码流程30分钟内完成
3. **密钥唯一性**：全局唯一约束
4. **审计日志**：记录所有密钥相关操作

### 用户教育

1. **保存提醒**：强调恢复密钥的重要性
2. **安全建议**：建议保存在密码管理器或安全位置
3. **备选方案**：提供重新注册的选项

## 用户体验设计

### 视觉设计

- 使用项目统一的设计语言
- 支持明暗主题切换
- 响应式布局适配移动端

### 交互设计

- 清晰的步骤指引
- 友好的错误提示
- 便捷的复制和下载功能

## 风险评估

### 技术风险

- **低风险**：基于现有架构扩展，技术栈成熟
- **迁移风险**：数据库迁移需要谨慎处理

### 用户风险

- **密钥丢失**：用户可能丢失恢复密钥
- **缓解措施**：提供重新注册选项，加强用户教育

## 验收标准

### 功能验收

- [ ] 注册流程包含恢复密钥生成和展示
- [ ] 忘记密码可以使用恢复密钥验证
- [ ] 安全设置可以查看和管理恢复密钥
- [ ] 支持中英文国际化
- [ ] 支持明暗主题

### 性能验收

- [ ] 恢复密钥生成响应时间 < 100ms
- [ ] 验证响应时间 < 200ms
- [ ] 页面加载时间 < 1s

### 安全验收

- [ ] 恢复密钥全局唯一
- [ ] 防止暴力破解攻击
- [ ] 敏感操作有审计日志

## 部署计划

### 部署步骤

1. 备份现有数据库
2. 执行数据库迁移
3. 部署后端代码
4. 部署前端代码
5. 验证功能正常

### 回滚计划

- 保留原有忘记密码功能作为备选
- 数据库迁移可回滚
- 代码版本可快速回退

## 后续优化

### 短期优化

- 添加恢复密钥使用统计
- 优化错误提示信息
- 增加密钥强度指示

### 长期规划

- 考虑支持多个恢复密钥
- 集成硬件安全密钥
- 添加生物识别验证

---

**文档版本**: v1.0  
**创建时间**: 2025-01-01  
**负责人**: 开发团队  
**预计完成时间**: 2-3个工作日
