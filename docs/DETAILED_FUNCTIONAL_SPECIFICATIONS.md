# Flow Balance - 详细功能规格说明书

## 📋 文档概述

本文档提供 Flow
Balance 个人财务管理系统的详细功能规格说明，包括每个功能模块的具体实现要求、用户交互流程、数据处理逻辑和技术实现细节。

## 🎯 产品定位

Flow
Balance 是一个专业的个人财务管理系统，基于现代财务会计理念，正确区分存量（资产负债）和流量（收入支出）概念，为个人用户提供企业级的财务分析和管理工具。

## 📚 功能模块详细规格

### 1. 用户认证与授权模块

#### 1.1 用户注册功能

**功能描述**：新用户账户创建和初始化

**输入要求**：

- 邮箱地址（必填，格式验证，唯一性检查）
- 密码（必填，最少8位，包含字母和数字）
- 确认密码（必填，与密码一致）

**处理流程**：

1. 前端表单验证（实时验证）
2. 后端数据验证（邮箱唯一性、密码强度）
3. 密码哈希处理（bcryptjs）
4. 用户记录创建
5. 自动跳转到登录页面（带setup参数）

**输出结果**：

- 成功：用户账户创建，返回用户信息（不含密码）
- 失败：返回具体错误信息

**技术实现**：

- API端点：`POST /api/auth/signup`
- 组件：`SignupForm.tsx`
- 验证：前端实时验证 + 后端安全验证

#### 1.2 用户登录功能

**功能描述**：用户身份验证和会话建立

**输入要求**：

- 邮箱地址（必填）
- 密码（必填）

**处理流程**：

1. 用户凭据验证
2. 密码哈希比对
3. JWT令牌生成
4. HTTP-only Cookie设置
5. 用户会话建立
6. 重定向处理（dashboard或setup）

**输出结果**：

- 成功：建立用户会话，跳转到相应页面
- 失败：显示错误信息，保持登录页面

**安全特性**：

- 密码哈希验证
- JWT令牌认证
- 会话过期管理
- 防暴力破解

#### 1.3 密码重置功能

**功能描述**：忘记密码的安全重置机制

**第一阶段 - 请求重置**：

- 输入：邮箱地址
- 处理：生成安全令牌，设置过期时间
- 输出：重置链接（模拟发送）

**第二阶段 - 密码重置**：

- 输入：重置令牌、新密码、确认密码
- 处理：令牌验证、密码更新、令牌清除
- 输出：重置成功，跳转登录页面

**安全机制**：

- 令牌唯一性和时效性
- 令牌使用后立即失效
- 密码强度验证

### 2. 初始设置模块

#### 2.1 货币选择功能

**功能描述**：用户可用货币配置

**界面设计**：

- 常用货币区域（USD, EUR, CNY, JPY等）
- 其他货币区域（按字母排序）
- 搜索功能（货币代码和名称）
- 多选复选框

**交互逻辑**：

- 至少选择一种货币才能继续
- 实时显示已选择货币数量
- 支持全选/取消全选
- 货币信息展示（代码、名称、符号）

**数据处理**：

- 创建UserCurrency记录
- 设置货币启用状态
- 配置显示顺序

#### 2.2 本位币设置功能

**功能描述**：主要货币选择和配置

**选择限制**：

- 只能从已选择的可用货币中选择
- 必须选择一种本位币
- 本位币用于汇总计算

**影响范围**：

- 仪表板统计显示
- 财务报表生成
- 汇率转换基准
- 图表数据展示

**技术实现**：

- 更新UserSettings表
- 设置baseCurrencyCode字段
- 验证货币可用性

### 3. 仪表板模块

#### 3.1 财务概览卡片

**净资产卡片**：

- 计算逻辑：总资产 - 总负债
- 显示格式：本位币金额，千位分隔符
- 变化指示：与上月对比，显示增减百分比
- 颜色编码：正值绿色，负值红色

**总资产卡片**：

- 计算范围：所有资产类账户余额汇总
- 货币转换：统一转换为本位币
- 明细链接：点击查看资产明细

**总负债卡片**：

- 计算范围：所有负债类账户余额汇总
- 显示方式：负债金额显示为正数
- 颜色标识：橙色主题

**本月收支卡片**：

- 收入统计：本月所有收入类交易汇总
- 支出统计：本月所有支出类交易汇总
- 净收支：收入 - 支出
- 趋势指示：与上月对比

#### 3.2 账户汇总组件

**分类展示**：

- 按账户分类分组显示
- 树状结构展示
- 展开/折叠功能
- 实时余额计算

**账户信息**：

- 账户名称和描述
- 当前余额（原币种）
- 本位币等值金额
- 货币标签显示

**快速操作**：

- 存量类账户：更新余额按钮
- 流量类账户：添加交易按钮
- 查看详情链接

#### 3.3 趋势图表

**净资产趋势图**：

- 图表类型：折线图
- 时间范围：最近12个月
- 数据点：每月末净资产值
- 交互功能：悬停显示详细数值

**现金流图表**：

- 图表类型：柱状图
- 数据内容：月度收入、支出对比
- 颜色区分：收入绿色，支出红色
- 净现金流：收入-支出的折线

**分类分布图**：

- 图表类型：饼图或环形图
- 数据内容：各分类资产/负债占比
- 颜色编码：每个分类独特颜色
- 百分比显示：占比和金额

#### 3.4 数据验证系统

**验证项目**：

- 账户类型设置完整性
- 交易数据一致性检查
- 汇率设置完整性验证
- 分类配置合理性检查

**验证结果展示**：

- 数据质量评分（0-100分）
- 具体问题列表
- 优化建议
- 快速修复链接

**实时更新**：

- 数据变更后自动重新验证
- 实时更新验证结果
- 问题解决后自动移除提醒

### 4. 账户管理模块

#### 4.1 分类管理功能

**分类树结构**：

- 无限层级支持
- 父子关系维护
- 拖拽排序功能
- 批量操作支持

**分类类型系统**：

- 资产类（ASSET）：现金、银行、投资、固定资产
- 负债类（LIABILITY）：信用卡、贷款、应付款
- 收入类（INCOME）：工资、投资收益、其他收入
- 支出类（EXPENSE）：生活费、娱乐、交通费

**分类操作**：

- 创建分类：名称、类型、父分类选择
- 编辑分类：修改名称、调整类型、移动层级
- 删除分类：检查关联账户，安全删除
- 排序调整：拖拽或手动设置权重

#### 4.2 账户管理功能

**账户创建**：

- 必填信息：账户名称、所属分类、货币类型
- 可选信息：账户描述、颜色标识
- 验证规则：名称唯一性、货币可用性
- 自动继承：分类的账户类型

**账户配置**：

- 货币设置：单一货币，创建后不可更改
- 显示设置：颜色选择、图标配置
- 排序权重：影响显示顺序
- 状态管理：启用/禁用状态

**账户操作差异化**：

**存量类账户（资产/负债）**：

- 主要操作：更新余额
- 操作方式：直接设置新余额值
- 记录方式：创建余额调整交易
- 关注重点：时点余额状态

**流量类账户（收入/支出）**：

- 主要操作：添加交易
- 操作方式：记录具体交易明细
- 记录方式：创建收入/支出交易
- 关注重点：期间流量汇总

#### 4.3 账户详情页面

**存量类账户详情**：

- 当前余额显示（大字体突出）
- 余额变化趋势图（折线图）
- 历史余额记录表格
- 余额更新操作按钮
- 最近余额调整记录

**流量类账户详情**：

- 累计流量统计
- 期间流量分析（月度、年度）
- 交易明细列表（分页显示）
- 添加交易操作按钮
- 流量趋势图表

**通用功能**：

- 交易列表：分页、筛选、排序
- 统计图表：趋势分析、对比分析
- 导出功能：CSV、Excel格式
- 编辑账户：基本信息修改
- 删除账户：安全删除检查

### 5. 交易管理模块

#### 5.1 交易类型系统

**基础交易类型**：

- 收入交易（INCOME）：增加资产、减少负债、记录收入流量
- 支出交易（EXPENSE）：减少资产、增加负债、记录支出流量
- 余额调整（BALANCE）：仅用于存量类账户余额调整

**交易验证规则**：

- 账户类型匹配：收入账户只能有收入交易，支出账户只能有支出交易
- 货币一致性：交易货币必须与账户货币一致
- 金额有效性：金额必须大于0
- 日期合理性：不能是未来日期

#### 5.2 交易创建功能

**交易表单设计**：

- 金额输入：数字键盘、千位分隔符显示
- 交易类型：根据账户类型智能选择
- 账户选择：分类树形选择器
- 日期选择：日期选择器，默认今天
- 描述输入：必填，简要说明
- 备注输入：可选，详细信息
- 标签选择：多选，支持新建标签

**智能辅助功能**：

- 历史交易模板：快速复用常用交易
- 金额计算器：支持简单计算
- 分类建议：基于历史记录智能推荐
- 标签自动完成：输入时自动提示

#### 5.3 余额更新功能

**适用范围**：仅限存量类账户（资产、负债）

**操作方式**：

- 直接输入新余额值
- 系统自动计算调整金额
- 创建余额调整交易记录
- 保持数据完整性和可追溯性

**使用场景**：

- 银行对账：根据银行账单更新
- 投资账户：根据市值变化更新
- 信用卡账单：根据账单更新负债
- 现金盘点：实际现金清点后更新

**安全机制**：

- 操作确认：显示调整金额确认
- 历史记录：保留所有调整记录
- 权限检查：只能操作自己的账户
- 数据验证：金额合理性检查

### 6. 多币种管理模块

#### 6.1 货币管理系统

**全球货币支持**：

- 预置货币库：包含主要国际货币（USD, EUR, GBP, JPY, CNY等）
- 货币信息：代码、名称、符号、小数位数
- 自定义货币：用户可创建专属货币
- 货币状态：启用/禁用状态管理

**用户可用货币**：

- 选择机制：从全球货币库中选择
- 管理界面：添加、移除、排序
- 使用限制：只能使用已添加的货币
- 状态控制：临时禁用不删除历史数据

#### 6.2 汇率管理系统

**汇率设置功能**：

- 手动输入：用户自定义汇率值
- 生效日期：设置汇率生效时间
- 备注信息：汇率来源或说明
- 历史管理：保留汇率变更历史

**汇率应用逻辑**：

- 转换方向：主要设置到本位币的汇率
- 查找策略：使用最近有效日期的汇率
- 缺失处理：提示用户设置缺失汇率
- 实时转换：统计时自动应用汇率

**汇率检查系统**：

- 自动检测：识别用户使用的货币
- 缺失提醒：Dashboard显示设置提醒
- 批量设置：一次性设置多个汇率
- 引导流程：一键跳转设置页面

#### 6.3 货币转换功能

**转换服务**：

- 单个转换：单笔金额货币转换
- 批量转换：多笔金额批量处理
- 缓存机制：避免重复计算
- 错误处理：转换失败的降级处理

**显示策略**：

- 双币显示：原币种 + 本位币等值
- 汇率透明：显示使用的汇率信息
- 转换标识：明确标识转换后金额
- 精度控制：保持合理的小数位数

### 7. 财务报表模块

#### 7.1 资产负债表

**报表结构设计**：

```
资产负债表
├── 资产
│   ├── 流动资产
│   │   ├── 现金类资产
│   │   └── 其他流动资产
│   ├── 非流动资产
│   │   ├── 投资资产
│   │   └── 固定资产
│   └── 资产总计
├── 负债
│   ├── 流动负债
│   │   ├── 短期借款
│   │   └── 应付款项
│   ├── 非流动负债
│   │   └── 长期借款
│   └── 负债总计
└── 净资产（资产总计 - 负债总计）
```

**数据计算逻辑**：

- 时点数据：反映特定时点的财务状况
- 余额导向：使用账户当前余额
- 分类汇总：按分类层级递归汇总
- 货币统一：转换为本位币显示

**报表功能**：

- 时点选择：选择特定日期的财务状况
- 对比分析：不同时点的对比
- 明细查看：点击查看分类明细
- 导出功能：PDF、Excel格式导出

#### 7.2 现金流量表

**报表结构设计**：

```
现金流量表
├── 经营活动现金流
│   ├── 收入项目
│   │   ├── 工资收入
│   │   └── 其他经营收入
│   ├── 支出项目
│   │   ├── 生活支出
│   │   └── 其他经营支出
│   └── 经营活动净现金流
├── 投资活动现金流
│   ├── 投资收益
│   ├── 投资支出
│   └── 投资活动净现金流
├── 筹资活动现金流
│   ├── 借款收入
│   ├── 还款支出
│   └── 筹资活动净现金流
└── 净现金流量（三项活动净现金流之和）
```

**数据计算逻辑**：

- 期间数据：反映特定期间的现金流动
- 流量导向：使用交易流水汇总
- 活动分类：按现金流活动性质分类
- 净额计算：收入减支出得净现金流

**报表功能**：

- 期间选择：月度、季度、年度报表
- 趋势分析：多期间现金流对比
- 活动分析：各活动现金流占比
- 预测功能：基于历史数据预测

#### 7.3 报表通用功能

**时间范围控制**：

- 预设范围：本月、本季、本年、上年
- 自定义范围：任意起止日期
- 快速切换：一键切换常用范围
- 范围验证：确保日期范围合理

**货币显示选项**：

- 本位币模式：统一显示本位币
- 原币种模式：显示原始货币
- 双币种模式：同时显示两种货币
- 汇率说明：显示使用的汇率信息

**导出和打印**：

- PDF导出：专业格式，适合存档
- Excel导出：可编辑格式，便于分析
- 图片导出：PNG格式，便于分享
- 打印功能：浏览器打印，格式优化

### 8. 用户设置模块

#### 8.1 个人信息设置

**基本信息管理**：

- 邮箱显示：当前登录邮箱（不可修改）
- 用户名设置：可选的显示名称
- 头像上传：支持图片上传和裁剪
- 个人简介：可选的个人描述

**密码安全管理**：

- 当前密码验证：修改前验证身份
- 新密码设置：强度检查和确认
- 密码历史：防止使用近期密码
- 安全提示：密码安全建议

#### 8.2 系统偏好设置

**界面显示设置**：

- 主题选择：明亮、暗黑、跟随系统
- 语言设置：中文、英文，实时切换
- 字体大小：小、中、大三档
- 动画效果：启用/禁用界面动画

**数据显示设置**：

- 日期格式：YYYY-MM-DD、DD/MM/YYYY等
- 数字格式：千位分隔符、小数位数
- 货币显示：符号位置、格式样式
- 时区设置：本地时区选择

#### 8.3 财务设置

**本位币管理**：

- 当前本位币：显示当前设置
- 更改本位币：从可用货币中选择
- 影响说明：更改对统计的影响
- 历史处理：历史数据转换策略

**可用货币管理**：

- 货币列表：显示已添加货币
- 添加货币：从全球货币库选择
- 移除货币：检查使用情况后移除
- 排序调整：拖拽调整显示顺序

#### 8.4 分类设置

**分类管理界面**：

- 树形结构：展示完整分类层级
- 在线编辑：直接在树中编辑名称
- 拖拽排序：调整分类顺序
- 批量操作：批量修改分类属性

**分类操作功能**：

- 创建分类：设置名称、类型、父分类
- 编辑分类：修改基本信息
- 移动分类：调整层级关系
- 删除分类：安全删除检查

**类型管理**：

- 类型设置：为分类指定账户类型
- 类型继承：子分类继承父分类类型
- 类型验证：确保类型设置合理
- 类型影响：说明类型对计算的影响

#### 8.5 汇率设置

**汇率管理界面**：

- 汇率列表：显示所有已设置汇率
- 货币对显示：清晰显示转换方向
- 生效日期：显示汇率生效时间
- 汇率值：支持高精度小数

**汇率操作功能**：

- 添加汇率：设置新的货币对汇率
- 编辑汇率：修改现有汇率值
- 删除汇率：移除不需要的汇率
- 批量导入：从文件批量导入汇率

**汇率检查功能**：

- 缺失检测：自动检测需要的汇率
- 设置向导：引导设置缺失汇率
- 验证检查：检查汇率合理性
- 更新提醒：提醒更新过期汇率

### 9. 用户界面规格

#### 9.1 响应式设计规格

**断点设置**：

- 移动端：< 768px
- 平板端：768px - 1024px
- 桌面端：> 1024px
- 大屏幕：> 1440px

**布局适配**：

- 移动端：单列布局，垂直堆叠
- 平板端：两列布局，侧边栏可收起
- 桌面端：三列布局，完整功能展示
- 大屏幕：宽松布局，更多空白空间

**交互适配**：

- 触摸友好：按钮大小适合手指点击
- 手势支持：滑动、拖拽等手势操作
- 键盘导航：完整的键盘导航支持
- 无障碍：符合WCAG 2.1 AA标准

#### 9.2 视觉设计规格

**颜色系统**：

- 主色调：蓝色系（#3B82F6）
- 辅助色：灰色系（#6B7280）
- 成功色：绿色系（#10B981）
- 警告色：黄色系（#F59E0B）
- 错误色：红色系（#EF4444）

**账户类型颜色**：

- 资产类：蓝色主题（#3B82F6）
- 负债类：橙色主题（#F97316）
- 收入类：绿色主题（#10B981）
- 支出类：红色主题（#EF4444）

**字体系统**：

- 主字体：系统默认字体栈
- 数字字体：等宽字体（Menlo, Monaco）
- 字体大小：12px、14px、16px、18px、24px、32px
- 字重：400（常规）、500（中等）、600（半粗）、700（粗体）

#### 9.3 交互设计规格

**表单设计**：

- 输入框：圆角边框，聚焦高亮
- 按钮：圆角设计，悬停效果
- 选择器：下拉菜单，搜索支持
- 日期选择：日历弹窗，快速选择

**反馈系统**：

- Toast通知：顶部滑入，自动消失
- 加载状态：骨架屏，进度指示
- 错误提示：红色边框，图标提示
- 成功反馈：绿色提示，勾选图标

**导航设计**：

- 面包屑：显示当前位置
- 侧边栏：可收起，树形结构
- 标签页：水平标签，活动状态
- 分页：数字分页，跳转支持

#### 9.4 数据可视化规格

**图表库选择**：

- 主要图表库：ECharts 5.x
- 图表类型：折线图、柱状图、饼图、面积图
- 交互功能：缩放、平移、悬停、点击
- 响应式：自适应容器大小

**图表配色**：

- 单色系：使用主题色渐变
- 多色系：使用预定义色板
- 对比色：确保足够的对比度
- 无障碍：支持色盲友好色板

**图表功能**：

- 数据标签：显示具体数值
- 图例说明：清晰的图例标识
- 工具提示：悬停显示详细信息
- 导出功能：支持图片导出

### 10. 技术实现规格

#### 10.1 前端技术规格

**React组件架构**：

- 函数式组件：使用React Hooks
- 组件分层：UI组件、业务组件、页面组件
- 状态管理：React Context + useReducer
- 副作用处理：useEffect + 自定义Hooks

**TypeScript类型系统**：

- 严格模式：启用所有严格检查
- 类型定义：完整的类型定义覆盖
- 接口设计：API接口类型定义
- 泛型使用：提高代码复用性

**样式系统**：

- CSS框架：Tailwind CSS 4.x
- 组件样式：原子化CSS类
- 主题系统：CSS变量 + 主题切换
- 响应式：移动优先的响应式设计

**性能优化**：

- 代码分割：React.lazy + Suspense
- 图片优化：Next.js Image组件
- 缓存策略：SWR数据缓存
- 懒加载：组件和路由懒加载

#### 10.2 后端技术规格

**API架构设计**：

- RESTful API：资源导向的API设计
- 统一响应：标准化的响应格式
- 错误处理：统一的错误处理机制
- 版本控制：API版本管理策略

**数据库设计**：

- ORM框架：Prisma 6.x
- 数据模型：关系型数据模型
- 迁移管理：版本化数据库迁移
- 查询优化：索引优化和查询优化

**认证授权**：

- JWT令牌：无状态认证机制
- 密码安全：bcryptjs哈希加密
- 会话管理：HTTP-only Cookie
- 权限控制：基于用户的数据隔离

**数据验证**：

- 输入验证：前后端双重验证
- 类型检查：TypeScript类型安全
- 业务规则：业务逻辑验证
- 安全防护：SQL注入、XSS防护

#### 10.3 数据库设计规格

**表结构设计**：

- 用户表：用户基本信息和认证
- 设置表：用户个性化设置
- 货币表：全球货币信息
- 汇率表：用户自定义汇率
- 分类表：账户分类层级结构
- 账户表：用户账户信息
- 交易表：所有交易记录
- 标签表：交易标签系统

**关系设计**：

- 一对一：用户-设置
- 一对多：用户-账户、分类-账户
- 多对多：交易-标签
- 自关联：分类父子关系

**约束设计**：

- 主键约束：所有表都有主键
- 外键约束：维护引用完整性
- 唯一约束：防止重复数据
- 检查约束：业务规则约束

**索引设计**：

- 主键索引：自动创建
- 外键索引：提高关联查询性能
- 复合索引：多字段查询优化
- 唯一索引：唯一性约束

#### 10.4 安全规格

**认证安全**：

- 密码策略：最少8位，包含字母数字
- 密码存储：bcryptjs哈希加密
- 令牌安全：JWT签名验证
- 会话安全：HTTP-only Cookie

**数据安全**：

- 数据隔离：用户数据严格隔离
- 权限检查：所有操作权限验证
- 输入验证：防止恶意输入
- 输出编码：防止XSS攻击

**传输安全**：

- HTTPS强制：生产环境强制HTTPS
- 安全头：设置安全相关HTTP头
- CORS配置：跨域请求安全配置
- CSP策略：内容安全策略

### 11. 业务流程规格

#### 11.1 用户注册流程

**流程步骤**：

1. 用户访问注册页面
2. 填写注册信息（邮箱、密码）
3. 前端实时验证输入
4. 提交注册请求
5. 后端验证数据有效性
6. 创建用户账户
7. 返回注册结果
8. 跳转到登录页面（带setup参数）

**异常处理**：

- 邮箱已存在：提示用户使用其他邮箱
- 密码不符合要求：显示密码要求
- 网络错误：提示重试
- 服务器错误：显示友好错误信息

#### 11.2 初始设置流程

**流程步骤**：

1. 用户首次登录后跳转到设置页面
2. 第一步：选择可用货币
   - 显示常用货币和其他货币
   - 用户选择需要使用的货币
   - 至少选择一种货币
3. 第二步：设置本位币
   - 从已选择货币中选择本位币
   - 说明本位币的作用
4. 第三步：完成设置
   - 保存用户设置
   - 创建默认分类
   - 跳转到仪表板

**设置验证**：

- 货币选择验证：至少选择一种
- 本位币验证：必须在可用货币中
- 设置保存验证：确保设置成功保存

#### 11.3 账户操作流程

**存量类账户操作流程**：

1. 用户进入存量类账户详情页
2. 点击"更新余额"按钮
3. 弹出余额更新模态框
4. 输入新的账户余额
5. 系统计算调整金额
6. 用户确认更新操作
7. 创建余额调整交易
8. 更新账户余额
9. 刷新页面数据

**流量类账户操作流程**：

1. 用户进入流量类账户详情页
2. 点击"添加交易"按钮
3. 弹出交易创建模态框
4. 填写交易信息
5. 选择交易类型和金额
6. 添加描述和标签
7. 提交交易创建请求
8. 验证交易数据
9. 创建交易记录
10. 更新账户统计

#### 11.4 多币种使用流程

**汇率设置流程**：

1. 用户添加多币种账户或交易
2. 系统检测缺失汇率
3. 在仪表板显示汇率提醒
4. 用户点击设置汇率
5. 跳转到汇率管理页面
6. 显示需要设置的汇率
7. 用户输入汇率值和生效日期
8. 保存汇率设置
9. 系统应用汇率进行转换
10. 更新统计数据显示

**货币转换流程**：

1. 用户查看统计数据
2. 系统识别不同货币的金额
3. 查找对应的汇率设置
4. 执行货币转换计算
5. 显示本位币等值金额
6. 提供原币种参考信息

#### 11.5 报表生成流程

**资产负债表生成流程**：

1. 用户选择报表日期
2. 系统查询指定日期的账户余额
3. 按分类汇总资产和负债
4. 计算净资产（资产-负债）
5. 应用汇率转换为本位币
6. 生成报表数据结构
7. 渲染报表界面
8. 提供导出功能

**现金流量表生成流程**：

1. 用户选择报表期间
2. 系统查询期间内的所有交易
3. 按交易类型分类汇总
4. 计算各活动的净现金流
5. 应用汇率转换为本位币
6. 生成报表数据结构
7. 渲染报表界面
8. 提供导出功能

### 12. 数据处理规格

#### 12.1 余额计算规格

**存量类账户余额计算**：

```typescript
// 资产类账户
if (accountType === 'ASSET') {
  // 收入增加余额，支出减少余额
  balance += transaction.type === 'INCOME' ? amount : -amount
}

// 负债类账户
if (accountType === 'LIABILITY') {
  // 支出增加余额，收入减少余额
  balance += transaction.type === 'EXPENSE' ? amount : -amount
}
```

**流量类账户流量计算**：

```typescript
// 收入类账户
if (accountType === 'INCOME') {
  // 只统计收入类型交易
  if (transaction.type === 'INCOME') {
    totalFlow += amount
  }
}

// 支出类账户
if (accountType === 'EXPENSE') {
  // 只统计支出类型交易
  if (transaction.type === 'EXPENSE') {
    totalFlow += amount
  }
}
```

#### 12.2 货币转换规格

**转换算法**：

```typescript
function convertCurrency(
  amount: number,
  fromCurrency: string,
  toCurrency: string,
  exchangeRate: number
): number {
  if (fromCurrency === toCurrency) {
    return amount
  }
  return amount * exchangeRate
}
```

**汇率查找策略**：

1. 查找指定日期的汇率
2. 如果没有，查找最近的历史汇率
3. 如果仍没有，提示用户设置汇率
4. 转换失败时返回原金额并标记

#### 12.3 数据验证规格

**输入数据验证**：

- 金额验证：必须为正数，最多2位小数
- 日期验证：不能为未来日期
- 文本验证：长度限制，特殊字符过滤
- 选择验证：必须在有效选项范围内

**业务逻辑验证**：

- 账户类型匹配：交易类型与账户类型匹配
- 货币一致性：交易货币与账户货币一致
- 权限验证：用户只能操作自己的数据
- 关联验证：删除时检查关联数据

#### 12.4 性能优化规格

**查询优化**：

- 索引使用：关键字段建立索引
- 查询合并：减少数据库查询次数
- 分页查询：大数据集分页处理
- 缓存策略：频繁查询数据缓存

**前端优化**：

- 虚拟滚动：长列表虚拟滚动
- 防抖节流：用户输入防抖处理
- 懒加载：组件和数据懒加载
- 内存管理：及时清理无用数据

---

## 📋 总结

本详细功能规格说明书涵盖了 Flow Balance 个人财务管理系统的所有核心功能模块，包括：

1. **用户认证与授权**：完整的用户管理体系
2. **初始设置系统**：用户友好的初始化流程
3. **仪表板系统**：直观的财务数据展示
4. **账户管理**：专业的账户分类和管理
5. **交易管理**：灵活的交易记录和处理
6. **多币种支持**：完整的多币种管理体系
7. **财务报表**：专业的财务分析报表
8. **用户设置**：个性化的系统配置
9. **界面设计**：现代化的用户界面
10. **技术实现**：可靠的技术架构
11. **业务流程**：清晰的操作流程
12. **数据处理**：准确的数据计算

该规格说明书为开发团队提供了详细的实现指导，确保系统能够满足用户的专业财务管理需求，同时保持良好的用户体验和系统性能。
