# Categories页面性能优化测试指南

## 优化内容概述

我们对categories页面进行了以下性能优化：

### 1. API接口优化

- **新增时间范围参数**: `/api/categories/[categoryId]/summary` 现在支持 `timeRange` 参数
- **默认行为**: 默认只返回去年1月1日至今的数据 (`timeRange=lastYear`)
- **全部数据**: 当 `timeRange=all` 时返回完整历史数据

### 2. 后端服务优化

- **Stock类别服务**: `getStockCategorySummary` 支持时间范围过滤
- **Flow类别服务**: `getFlowCategorySummary` 支持时间范围过滤
- **数据库查询优化**: 只查询指定时间范围内的交易记录

### 3. 前端组件优化

- **懒加载**: 页面初始加载只获取去年至今的数据
- **按需加载**: 点击"全部"按钮时才获取完整历史数据
- **图表组件**: 支持时间范围切换和数据重新加载

## 测试步骤

### 1. 准备测试环境

```bash
# 启动开发服务器
pnpm dev
```

### 2. 浏览器测试

1. 打开浏览器开发者工具 (F12)
2. 切换到 Network 标签页
3. 访问任意分类详情页面 (例如: `/categories/[id]`)

### 3. 性能对比测试

#### 测试默认加载性能

1. 刷新页面，观察 Network 标签中的API请求
2. 查找 `/api/categories/[id]/summary` 请求
3. 记录请求时间和响应大小
4. 查看返回的月份数据量

#### 测试全部数据加载性能

1. 在分类页面点击图表上的"全部"按钮
2. 观察新的API请求 `/api/categories/[id]/summary?timeRange=all`
3. 记录请求时间和响应大小
4. 对比数据量差异

### 4. 预期结果

#### 性能提升指标

- **默认加载**: 应该明显快于全部数据加载
- **数据量**: 默认加载的月份数量应该少于全部数据
- **用户体验**: 页面初始加载更快，用户可以立即看到最近的数据

#### 功能验证

- ✅ 页面默认显示去年至今的数据
- ✅ 图表显示正确的时间范围
- ✅ 点击"全部"按钮能正确加载完整历史数据
- ✅ 时间范围切换功能正常工作

## 测试用例

### 用例1: 新用户/少量数据

- **场景**: 用户数据较少，历史不超过1年
- **预期**: 默认加载和全部加载性能差异不大
- **验证**: 功能正常，无性能问题

### 用例2: 老用户/大量历史数据

- **场景**: 用户有多年历史数据
- **预期**: 默认加载明显快于全部加载
- **验证**: 显著的性能提升

### 用例3: 图表交互

- **场景**: 在图表上切换时间范围
- **预期**: 切换流畅，数据正确更新
- **验证**: 用户体验良好

## 性能监控

### 关键指标

1. **API响应时间**: 默认加载 vs 全部加载
2. **数据传输量**: 响应体大小对比
3. **前端渲染时间**: 图表渲染速度
4. **用户感知性能**: 页面可交互时间

### 监控方法

- 使用浏览器开发者工具的 Performance 标签
- 观察 Network 标签中的请求详情
- 使用 Lighthouse 进行性能评估

## 故障排除

### 常见问题

1. **API返回错误**: 检查时间范围参数是否正确
2. **数据不更新**: 确认图表组件的回调函数正常工作
3. **性能无改善**: 可能数据量不足以体现差异

### 调试技巧

- 在浏览器控制台查看API请求和响应
- 检查服务器日志中的查询执行时间
- 使用数据库查询分析工具监控查询性能
