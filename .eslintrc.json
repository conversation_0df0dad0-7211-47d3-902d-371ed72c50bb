{"extends": ["next/core-web-vitals"], "rules": {"no-console": ["warn", {"allow": ["warn", "error"]}], "@typescript-eslint/no-explicit-any": "error", "@typescript-eslint/no-non-null-assertion": "error", "@typescript-eslint/no-unused-vars": ["warn", {"argsIgnorePattern": "^_"}], "react-hooks/exhaustive-deps": "warn", "max-len": ["warn", {"code": 120, "ignoreUrls": true, "ignoreStrings": true}], "react/no-unknown-property": ["error", {"ignore": ["jsx", "global"]}], "comma-dangle": "off", "@typescript-eslint/comma-dangle": "off"}}