{"smart.paste.title": "Smart Paste", "smart.paste.intelligent.batch.entry": "Intelligent Batch Entry", "smart.paste.batch.entry": "Batch Entry", "smart.paste.batch.edit": "<PERSON>ch Edit", "smart.paste.quick.batch.operation": "Quick Batch Operation", "smart.paste.modal.title.income": "Income Batch Entry", "smart.paste.modal.title.expense": "Expense <PERSON>", "smart.paste.modal.title.batch.edit": "Batch Edit - {{accountName}} ({{count}} records)", "smart.paste.modal.title.batch.edit.multi": "Batch Edit ({{count}} records)", "smart.paste.modal.title.batch.entry": "Batch Entry - {{accountName}}", "smart.paste.modal.title.batch.entry.multi": "Batch Entry", "smart.paste.account.selector.default": "Default Account:", "smart.paste.account.selector.target": "Target Account:", "smart.paste.account.selector.mixed": "Mixed Accounts (select in table)", "smart.paste.account.selector.hint.single": "All transactions will be recorded to this account", "smart.paste.account.selector.hint.mixed": "Select account for each transaction in the table", "smart.paste.grid.column.date": "Date", "smart.paste.grid.column.amount": "Amount", "smart.paste.grid.column.description": "Description", "smart.paste.grid.column.notes": "Notes", "smart.paste.grid.column.tags": "Tags", "smart.paste.grid.column.account": "Account", "smart.paste.grid.column.category": "Category", "smart.paste.toolbar.add.row": "Add Row", "smart.paste.toolbar.delete.selected": "Delete Selected Rows", "smart.paste.toolbar.undo": "Undo", "smart.paste.toolbar.redo": "Redo", "smart.paste.toolbar.copy": "Copy", "smart.paste.toolbar.paste": "Paste", "smart.paste.toolbar.validate": "Validate Data", "smart.paste.toolbar.submit": "Submit Data", "smart.paste.validation.summary": "Validation Summary", "smart.paste.validation.total.rows": "Total Rows: {{count}}", "smart.paste.validation.active.rows": "Active Rows: {{count}}", "smart.paste.validation.valid.rows": "Valid: {{count}}", "smart.paste.validation.invalid.rows": "Invalid: {{count}}", "smart.paste.validation.partial.rows": "Partial: {{count}}", "smart.paste.validation.empty.rows": "Empty: {{count}}", "smart.paste.cell.placeholder.date": "Select date", "smart.paste.cell.placeholder.amount": "Enter amount", "smart.paste.cell.placeholder.description": "Enter description", "smart.paste.cell.placeholder.notes": "Enter notes", "smart.paste.cell.placeholder.tags": "Select tags", "smart.paste.cell.placeholder.account": "Select account", "smart.paste.tag.selector.title": "Select Tags", "smart.paste.tag.selector.search": "Search tags...", "smart.paste.tag.selector.no.results": "No tags found", "smart.paste.tag.selector.selected": "{{count}} tags selected", "smart.paste.account.selector.title": "Select Account", "smart.paste.account.selector.search": "Search accounts...", "smart.paste.account.selector.no.results": "No accounts found", "smart.paste.date.picker.today": "Today", "smart.paste.date.picker.clear": "Clear", "smart.paste.copy.success": "Copy successful", "smart.paste.copy.success.detail": "Copied {{count}} cells", "smart.paste.copy.error": "Co<PERSON> failed", "smart.paste.empty.hint": "Please paste or add data to start entry", "smart.paste.paste.column.success": "Column paste successful", "smart.paste.paste.column.success.detail": "Pasted {{count}} rows to {{column}} column", "smart.paste.multi.cell.paste": "Multi-cell paste", "smart.paste.cells": "cells", "smart.paste.multi.cell.success": "Pasted to {{count}} cells", "smart.paste.submit.no.valid.data": "No valid data to submit", "smart.paste.submit.processing": "Submitting data...", "smart.paste.submit.success.create": "Batch entry successful", "smart.paste.submit.success.create.detail": "Successfully created {{count}} transaction records", "smart.paste.submit.success.update": "<PERSON>ch edit successful", "smart.paste.submit.success.update.detail": "Successfully updated {{count}} transaction records", "smart.paste.submit.error": "Batch entry failed", "smart.paste.submit.error.update": "Batch edit failed", "smart.paste.toolbar.submit.with.errors": "Submit Data (With Errors)", "smart.paste.submit.confirm.title": "Confirm Data Submission", "smart.paste.submit.confirm.message": "{{invalid}} rows contain validation errors. Only {{valid}} valid rows will be submitted (out of {{total}} total rows).", "smart.paste.submit.confirm.button": "Confirm Submit Valid Data", "smart.paste.submit.confirm.statistics": "Data Statistics", "smart.paste.submit.confirm.valid": "Valid Data", "smart.paste.submit.confirm.invalid": "Error Data", "smart.paste.submit.confirm.note": "Error data will be skipped and not submitted to the system.", "smart.paste.keyboard.shortcuts": "Keyboard Shortcuts", "smart.paste.keyboard.copy": "Copy: Ctrl+C / Cmd+C", "smart.paste.keyboard.paste": "Paste: Ctrl+V / Cmd+V", "smart.paste.keyboard.undo": "Undo: Ctrl+Z / Cmd+Z", "smart.paste.keyboard.redo": "Redo: Ctrl+Y / Cmd+Shift+Z", "smart.paste.keyboard.select.all": "Select All: Ctrl+A / Cmd+A", "smart.paste.keyboard.delete": "Delete: Delete / Backspace", "smart.paste.keyboard.escape": "Cancel Selection: Escape", "smart.paste.error.account.not.found": "Account not found: {{accountId}}", "smart.paste.error.invalid.date": "Invalid date format", "smart.paste.error.invalid.amount": "Invalid amount format", "smart.paste.error.required.field": "{{field}} is required", "smart.paste.balance.update.description": "Balance Update - {{accountName}}", "smart.paste.validation.passed": "✅ Data validation passed"}