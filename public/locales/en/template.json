{"template.name": "Template Name", "template.select.placeholder": "Select template or enter new template name", "template.select.label": "Transaction Template", "template.search.placeholder": "Search templates...", "template.no.templates": "No templates available", "template.no.results": "No matching templates found", "template.create.success": "Template created successfully", "template.create.failed": "Failed to create template", "template.create.hint": "Enter template name and submit transaction to save template automatically", "template.update.success": "Template updated successfully", "template.update.failed": "Failed to update template", "template.update.confirm.title": "Update Template Data", "template.update.confirm.description": "Save current changes to template \"{{templateName}}\"?", "template.update.confirm.hint": "Uncheck to submit transaction only without updating template", "template.delete.success": "Template deleted successfully", "template.delete.failed": "Failed to delete template", "template.delete.title": "Delete Template", "template.delete.confirm": "Delete", "template.delete.confirm.message": "Are you sure you want to delete template \"{{name}}\"? This action cannot be undone.", "template.load.success": "Template loaded successfully", "template.load.failed": "Failed to load template", "template.validation.name.required": "Template name is required", "template.validation.name.maxLength": "Template name cannot exceed 100 characters", "template.validation.name.exists": "Template name already exists, please use a different name", "template.validation.account.required": "Please select an account", "template.validation.account.invalid": "Account does not exist or access denied", "template.validation.type.mismatch": "Transaction type does not match account type", "template.form.save": "Save as Template", "template.form.update": "Update Template", "template.form.apply": "Apply Template"}