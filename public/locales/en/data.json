{"data.no.results": "No results found", "data.empty.state": "No data to display", "data.loading.error": "Error loading data", "data.retry": "Retry", "data.refresh": "Refresh Data", "data.last.updated": "Last updated: {{time}}", "data.export.title": "Export Data", "data.export.description": "Export all your data including accounts, categories, transaction records, etc. Data will be downloaded in JSON format for easy backup or migration to other systems.", "data.export.includes": "Export includes:", "data.export.includes.accounts": "Account and category information", "data.export.includes.transactions": "All transaction records", "data.export.includes.tags": "Tags and currency settings", "data.export.includes.rates": "Exchange rates and transaction templates", "data.export.includes.recurring": "Recurring transactions and loan contracts", "data.export.includes.preferences": "User preference settings", "data.export.accounts": "All account information and balances", "data.export.transactions": "All transaction records", "data.export.categories": "Categories and tag settings", "data.export.currencies": "Currency and exchange rate configurations", "data.export.preferences": "Personal preference settings", "data.export.button": "Export Data", "data.export.exporting": "Exporting...", "data.export.success": "Data exported successfully", "data.export.success.message": "Your data has been downloaded to your device", "data.export.failed": "Export failed", "data.export.network.error": "Export failed: Network error", "data.import.title": "Data Import", "data.import.description": "Import previously exported data files to restore your financial records. The import process automatically handles ID mapping to ensure data integrity.", "data.import.features": "Import features:", "data.import.features.mapping": "Automatic ID remapping", "data.import.features.duplicates": "Smart duplicate data handling", "data.import.features.relations": "Maintain data relationships", "data.import.features.recovery": "Support partial import and error recovery", "data.import.features.cross": "Account-independent, supports cross-account import", "data.import.button": "Select Import File", "data.import.validating": "Validating...", "data.import.importing": "Importing...", "data.import.confirm.title": "Confirm Data Import", "data.import.confirm.button": "Confirm Import", "data.import.file.info": "Data File Information", "data.import.file.version": "Version", "data.import.file.export.time": "Export Time", "data.import.file.app": "Application", "data.import.file.unknown": "Unknown", "data.import.statistics": "Data Statistics", "data.import.statistics.categories": "Categories", "data.import.statistics.accounts": "Accounts", "data.import.statistics.transactions": "Transactions", "data.import.statistics.transactions.manual": "Manual Transactions", "data.import.statistics.transactions.recurring": "Recurring Transaction Records", "data.import.statistics.transactions.loan": "Loan-related Transactions", "data.import.statistics.tags": "Tags", "data.import.statistics.currencies": "Currencies", "data.import.statistics.rates": "Exchange Rates", "data.import.statistics.templates": "Transaction Templates", "data.import.statistics.recurring": "Recurring Transactions", "data.import.statistics.loans": "Loan Contracts", "data.import.statistics.payments": "Loan Payments", "data.import.selector.title": "Select Data to Import", "data.import.selector.select.all": "Select All", "data.import.selector.selected.summary": "Selected {{selected}}/{{total}} items", "data.import.selector.total.records": "{{count}} total records", "data.import.selector.required": "Required", "data.import.selector.depends.on": "Depends on", "data.import.selector.dependency.note": "Data types have dependencies, selecting an item will automatically select its dependencies. Transaction records include manual transactions, recurring transaction records, and loan-related transactions.", "data.import.section.recurring": "Recurring Transactions", "data.import.section.loans": "Loan Contracts", "data.import.required.title": "Required Data", "data.import.required.auto": "Auto Import", "data.import.required.label": "Required", "data.import.total.records": "Total Records", "data.import.unit.records": "items", "data.import.selector.dependency.accounts": "Accounts depend on categories, templates and recurring transactions depend on accounts", "data.import.selector.dependency.payments": "Loan payments depend on loan contracts", "data.import.selector.dependency.rates": "Exchange rates depend on currency settings", "data.import.selector.categories.desc": "Account categories and hierarchical structure", "data.import.selector.accounts.desc": "Bank accounts, credit cards and other financial accounts", "data.import.selector.transactions.desc": "All transaction records (including manual transactions, recurring transaction records, loan-related transactions)", "data.import.selector.transactions.manual.desc": "Transaction records created directly by users", "data.import.selector.transactions.recurring.desc": "Transaction records automatically generated by recurring transaction rules", "data.import.selector.transactions.loan.desc": "Transaction records related to loan disbursements, repayments, etc.", "data.import.selector.tags.desc": "Transaction tags and category markers", "data.import.selector.currencies.desc": "Currency settings and exchange rate configuration", "data.import.selector.rates.desc": "Exchange rate data between currencies", "data.import.selector.templates.desc": "Common transaction templates", "data.import.selector.recurring.desc": "Recurring income and expense settings", "data.import.selector.loans.desc": "Loan contract information", "data.import.selector.payments.desc": "Loan payment schedules and records", "data.import.validation.errors": "Validation Errors", "data.import.validation.warnings": "Warnings", "data.import.options": "Import Options", "data.import.options.skip.duplicates": "Skip duplicate data", "data.import.options.overwrite": "Overwrite existing data", "data.import.options.create.currencies": "Create missing currencies", "data.import.validation.complete": "Data validation complete", "data.import.data.required": "Import data is required", "data.import.format.invalid": "Invalid import data format", "data.import.session.id.required": "Session ID is required", "data.import.session.not.found": "Import session not found", "data.import.validation.warnings.found": "Found {{count}} warnings, please review details", "data.import.validation.success": "Data validation successful", "data.import.validation.success.message": "Data format is correct, ready for import", "data.import.validation.failed": "Data validation failed", "data.import.integrity.check.failed": "Data integrity check failed: {{error}}", "data.import.version.unsupported": "Unsupported data version {{version}}, supported versions: {{supported}}", "data.import.json.format.error": "Import data format error, please ensure it's a valid JSON format", "data.import.file.format.error": "File format error", "data.import.file.format.error.message": "Please ensure you select a valid JSON file", "data.import.validation.error": "Validation failed", "data.import.validation.error.message": "Error occurred during file reading or validation", "data.import.success": "Data import successful", "data.import.success.message": "Successfully created {{created}} records, updated {{updated}} records{{skipped}}", "data.import.success.skipped": ", skipped {{count}} records", "data.import.complete": "Import complete", "data.import.completed": "Import completed: created {{created}} records, updated {{updated}} records", "data.import.complete.warnings": "{{count}} warnings found, please review details", "data.import.failed": "Data import failed", "data.import.partial.success": "Import partially successful: {{failed}} records failed", "data.import.error": "Import failed", "data.import.error.message": "Error occurred during import process", "data.import.starting": "Starting data import...", "data.import.started": "Import started, please use session ID to query progress", "data.import.start.failed": "Failed to start import", "data.import.start.error": "Error occurred while starting import process", "data.import.progress.failed": "Import Failed", "data.import.cancelled": "Import cancelled", "data.import.timeout": "Import timeout", "data.import.timeout.message": "Import process timed out, please check import status", "data.import.invalid.file": "Invalid file format", "data.import.invalid.file.message": "Please select a JSON format file", "data.import.cancel.success": "Import cancelled", "data.import.cancel.failed": "Cancel import", "data.import.cancel.failed.message": "Unable to cancel import", "data.import.cancel.error": "Failed to cancel import", "data.import.progress.title": "Data Import Progress", "data.import.progress.validating": "Validating Data", "data.import.progress.importing": "Importing Data", "data.import.progress.completed": "Import Completed", "data.import.progress.processing": "Processing", "data.import.progress.details": "Details", "data.import.progress.session.id": "Session ID", "data.import.progress.stage": "Current Stage", "data.import.progress.items": "Items Processed", "error.network": "Network error", "data.delete.title": "Delete Account", "data.delete.warning": "Warning: This operation will permanently delete your account and all related data", "data.delete.includes": "Data to be deleted includes", "data.delete.accounts": "All account and balance information", "data.delete.transactions": "All transaction records", "data.delete.categories": "All categories and tags", "data.delete.settings": "Personal settings and preferences", "data.delete.irreversible": "This operation cannot be undone! It is recommended to export your data before deletion.", "data.delete.button": "Delete My Account", "data.delete.confirm.title": "Confirm Account Deletion", "data.delete.confirm": "Confirm Delete", "data.delete.confirm.description": "This operation will permanently delete your account and all related data, including:", "data.delete.confirm.list.accounts": "All accounts and transaction records", "data.delete.confirm.list.categories": "Categories and tags", "data.delete.confirm.list.settings": "User settings and preferences", "data.delete.confirm.list.loans": "Loan contracts and recurring transactions", "data.delete.password.prompt": "Please enter your password to confirm deletion:", "data.delete.password.placeholder": "Enter password", "data.delete.password.required": "Please enter password to confirm deletion", "data.delete.success": "Account deleted successfully", "data.delete.success.message": "Your account and all data have been permanently deleted", "data.delete.failed": "Deletion failed", "data.delete.network.error": "Deletion failed: Network error", "data.loading": "Processing data...", "common.cancel": "Cancel", "error.unknown": "Unknown error"}