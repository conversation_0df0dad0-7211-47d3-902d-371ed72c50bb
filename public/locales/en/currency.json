{"currency.currency": "<PERSON><PERSON><PERSON><PERSON>", "currency.label": "<PERSON><PERSON><PERSON><PERSON>", "currency.all": "All Currencies", "currency.management": "Currency Management", "currency.management.description": "Manage the currencies you can use. Only currencies added to the available list can be used in accounts, transactions, and exchange rate settings.", "currency.selected": "Selected Currencies", "currency.available": "Available Currencies", "currency.none.selected": "No currencies selected yet", "currency.add.instruction": "Please add the currencies you need from below", "currency.custom": "Custom", "currency.custom.create": "Create Custom Currency", "currency.custom.create.success": "Custom currency created successfully", "currency.custom.edit": "Edit Custom Currency", "currency.custom.update.failed": "Failed to update custom currency", "currency.code": "Currency Code", "currency.name": "Currency Name", "currency.symbol": "Currency Symbol", "currency.decimalPlaces": "Decimal Places", "currency.decimalPlaces.unit": "digits", "currency.decimalPlaces.help": "Set the number of decimal places for this currency (0-10 digits)", "currency.code.placeholder": "e.g: BTC, USDT", "currency.name.placeholder": "e.g: <PERSON><PERSON>in, <PERSON><PERSON>", "currency.symbol.placeholder": "e.g: ₿, ₮", "currency.code.help": "3-10 uppercase letters or numbers", "currency.add": "Add", "currency.remove": "Remove", "currency.add.failed": "Failed to add currency", "currency.remove.failed": "Failed to remove currency", "currency.update.failed": "Failed to update currency settings", "currency.form.incomplete": "Please fill in complete currency information", "currency.custom.create.failed": "Failed to create custom currency", "currency.custom.delete.failed": "Failed to delete custom currency", "currency.important.tips": "Important Tips", "currency.tip.custom.create": "You can create custom currencies (such as cryptocurrencies, points, etc.)", "currency.tip.code.format": "Custom currency codes must be 3-10 uppercase letters or numbers", "currency.tip.decimal.places": "Decimal places determine the precision of currency amount display, recommended to set according to actual usage scenarios", "currency.tip.delete.warning": "Before deleting a currency, ensure there are no related transaction records and exchange rate settings", "currency.tip.base.currency": "Base currency cannot be deleted. To change it, please modify the base currency in preference settings first", "currency.tip.major.currency": "It is recommended to keep at least one major currency (such as USD, EUR, CNY)", "currency.setup.required": "Base Currency Setup Required", "currency.setup.description": "Please set your base currency first to properly display amounts and perform currency conversions.", "currency.setup.action": "Go to Settings", "currency.setup.inline": "Please set base currency first", "currency.converter.title": "Currency Converter", "currency.converter.amount": "Amount", "currency.converter.no.rates": "No exchange rates available", "currency.converter.reverse.note": "Calculated from reverse rate", "currency.add.success": "Currency added successfully", "currency.remove.success": "Currency removed successfully", "currency.custom.delete.success": "Custom currency deleted successfully", "currency.invalid.currency": "Invalid currency", "currency.decimal.places.invalid": "Decimal places must be between 0-10", "currency.not.found": "Specified currency does not exist", "currency.permission.denied": "You do not have permission to use this currency, please add it in currency management first", "currency.get.failed": "Failed to get currency list", "currency.custom.fields.required": "Currency code, name and symbol are all required", "currency.custom.decimal.places.invalid": "Decimal places must be an integer between 0-10", "currency.custom.code.format.invalid": "Currency code must be 3-10 uppercase letters or numbers", "currency.custom.code.already.exists": "You have already created this currency code", "currency.custom.code.already.selected": "You have already selected another currency with code {{code}}, each currency code can only be selected once", "currency.custom.get.failed": "Failed to get custom currencies", "currency.custom.cannot.delete.base": "Cannot delete base currency, please change base currency settings first", "currency.custom.has.transactions": "This currency has {{count}} transaction records and cannot be deleted", "currency.custom.has.exchange.rates": "This currency has {{count}} exchange rate settings and cannot be deleted", "currency.custom.name.symbol.required": "Name and symbol are both required", "currency.custom.update.success": "Custom currency updated successfully"}