{"loan.contracts.empty.title": "No Loan Contracts", "loan.contracts.empty.description": "This account has no loan contracts created yet", "loan.status.active": "Active", "loan.status.completed": "Completed", "loan.status.paused": "Paused", "loan.status.cancelled": "Cancelled", "loan.principal.amount": "Principal Amount", "loan.interest.rate": "Interest Rate", "loan.payment.frequency": "Payment Frequency", "loan.start.date": "Start Date", "loan.end.date": "End Date", "loan.payment.account": "Payment Account", "loan.payment.account.click.to.view": "Click to view account details", "loan.monthly.payment": "Monthly Payment", "loan.contract.name": "Contract Name", "loan.contract.description": "Contract Description", "loan.contract.create": "Create <PERSON><PERSON> Contract", "loan.contract.edit": "Edit <PERSON><PERSON>tract", "loan.contract.delete": "Delete Loan Contract", "loan.contract.save": "Save Contract", "loan.contract.cancel": "Cancel", "loan.contract.status": "Contract Status", "loan.contract.status.active": "Active", "loan.contract.status.inactive": "Inactive", "loan.contract.delete.title": "Delete Loan Contract", "loan.contract.delete.message": "Are you sure you want to delete loan contract '{name}'?", "loan.contract.delete.related.data": "Related Data Statistics", "loan.contract.delete.balance.transactions": "Balance Adjustment Records", "loan.contract.delete.payment.transactions": "Payment Transaction Records", "loan.contract.delete.payment.records": "Payment Records", "loan.contract.delete.preserve.options": "Preserve Options", "loan.contract.delete.preserve.balance.transactions": "Preserve Balance Adjustment Records", "loan.contract.delete.preserve.balance.description": "Preserve {count} balance adjustment records, only remove loan contract association", "loan.contract.delete.preserve.payment.transactions": "Preserve Payment Transaction Records", "loan.contract.delete.preserve.payment.description": "Preserve {count} payment transaction records, only remove loan contract association", "loan.contract.delete.warning": "This operation cannot be undone, please proceed with caution!", "loan.contract.delete.confirm": "Confirm Delete", "loan.total.periods.edit.hint": "Must be greater than completed periods", "loan.total.periods.edit.description": "Completed periods: {completedPeriods}, Original periods: {originalPeriods}, new periods must be greater than completed periods", "loan.total.periods.edit.validation": "New total periods must be greater than completed periods ({completedPeriods})", "loan.payment.selected.count": "{count} records selected", "loan.payment.reset": "Reset", "loan.payment.reset.all": "Reset All", "loan.payment.reset.success": "Reset successful", "loan.payment.reset.error": "Reset failed", "loan.payment.reset.all.success": "Reset all successful", "loan.payment.reset.all.error": "Reset all failed", "loan.payment.reset.success.message": "Successfully reset {resetCount} payment records and deleted {deletedTransactions} transaction records", "loan.payment.reset.confirm.title": "Confirm Reset Payment Records", "loan.payment.reset.confirm.message": "Are you sure you want to reset the selected {count} payment records? This will delete related transaction records and cannot be undone.", "loan.payment.reset.all.confirm.title": "Confirm Reset All Payment Records", "loan.payment.reset.all.confirm.message": "Are you sure you want to reset all completed payment records for this loan contract? This will delete all related transaction records and cannot be undone.", "frequency.monthly": "Monthly", "frequency.quarterly": "Quarterly", "frequency.semi_annually": "Semi-annually", "frequency.annually": "Annually", "frequency.weekly": "Weekly", "frequency.bi_weekly": "Bi-weekly", "loan.type.mortgage": "Mortgage", "loan.type.personal": "Personal Loan", "loan.type.auto": "Auto Loan", "loan.type.business": "Business Loan", "loan.type.student": "Student Loan", "loan.type.other": "Other", "loan.calculation.method": "Calculation Method", "loan.calculation.equal_payment": "Equal Payment", "loan.calculation.equal_principal": "Equal Principal", "loan.calculation.interest_only": "Interest Only", "loan.remaining.balance": "Remaining Balance", "loan.total.interest": "Total Interest", "loan.total.payment": "Total Payment", "loan.next.payment.date": "Next Payment Date", "loan.next.payment.amount": "Next Payment Amount", "loan.contracts": "Loan Contracts", "loan.contracts.description": "Manage loan contract information for this account", "loan.contract.created": "Loan contract created", "loan.contract.updated": "Loan contract updated", "loan.status": "Status", "loan.contract.name.placeholder": "Enter contract name", "loan.contract.name.required": "Please enter contract name", "loan.transaction.template": "Transaction Template", "loan.transaction.description.placeholder": "Payment - {contractName}", "loan.transaction.notes.placeholder": "Period {period} Payment", "mortgage.loan.transaction.template": "Transaction Template", "mortgage.loan.transaction.description.placeholder": "Mortgage Payment", "mortgage.loan.transaction.notes.placeholder": "Period {period} Mortgage Payment", "mortgage.loan.contract.name.placeholder": "Enter mortgage contract name", "loan.amount.required": "Please enter a valid loan amount", "loan.interest.rate.required": "Please enter a valid interest rate", "loan.start.date.required": "Please select a start date", "loan.total.periods.required": "Please enter a valid total periods", "loan.payment.day.range": "Payment day must be between 1-31", "loan.payment.loading.description": "Loading payment records, please wait...", "loan.contract.deleted": "Loan contract deleted"}