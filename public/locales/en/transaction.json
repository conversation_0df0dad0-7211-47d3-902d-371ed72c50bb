{"transaction.amount": "Amount", "transaction.date": "Date", "transaction.description": "Description", "transaction.category": "Category", "transaction.account": "Account", "transaction.type": "Type", "transaction.type.balance": "Balance", "transaction.create": "Create Transaction", "transaction.edit": "Edit Transaction", "transaction.delete": "Delete Transaction", "transaction.modal.title": "Add Transaction", "transaction.modal.edit.title": "Edit Transaction", "transaction.modal.operation.tips": "💡 Operation Tips", "transaction.modal.flow.account.tip": "• This form is used to record transaction details for flow accounts", "transaction.modal.stock.account.tip": "• For managing stock accounts (assets/liabilities), please use the \"Balance Update\" function", "transaction.modal.auto.category.tip": "• The system will automatically set the corresponding transaction type and category based on the selected account", "transaction.modal.transaction.type": "Transaction Type", "transaction.modal.transaction.date": "Transaction Date", "transaction.modal.account": "Account", "transaction.modal.account.help": "Only income or expense accounts can be selected for transaction recording", "transaction.modal.category.auto.set": "Category automatically set to:", "transaction.modal.amount": "Amount", "transaction.modal.currency": "<PERSON><PERSON><PERSON><PERSON>", "transaction.modal.description": "Description", "transaction.modal.description.placeholder": "Please enter transaction description", "transaction.modal.notes": "Notes", "transaction.modal.notes.placeholder": "Optional notes", "transaction.modal.tags": "Tags", "transaction.modal.create.new.tag": "+ Create New Tag", "transaction.modal.cancel.new.tag": "Cancel", "transaction.modal.tag.name.placeholder": "Enter tag name", "transaction.modal.creating": "Creating...", "transaction.modal.create": "Create", "transaction.modal.tag.auto.add.tip": "Created tags will be automatically added to the current transaction", "transaction.modal.no.tags": "No tags yet", "transaction.modal.create.first.tag": "Create first tag", "transaction.modal.cancel": "Cancel", "transaction.modal.update.transaction": "Update Transaction", "transaction.modal.create.transaction": "Create Transaction", "transaction.modal.create.success": "Transaction created successfully", "transaction.modal.update.success": "Transaction updated successfully", "transaction.modal.balance.adjustment.error": "Balance adjustment records cannot be edited through the transaction form, please use the balance update function", "transaction.modal.select.account": "Please select account", "transaction.modal.select.category": "Please select category", "transaction.modal.select.currency": "Please select currency", "transaction.modal.select.type": "Please select transaction type", "transaction.modal.enter.amount": "Please enter amount", "transaction.modal.amount.positive": "Amount must be a positive number", "transaction.modal.enter.description": "Please enter description", "transaction.modal.select.date": "Please select date", "transaction.modal.operation.failed": "Operation failed", "transaction.list": "Transaction List", "transaction.list.subtitle": "View and manage all transaction records", "transaction.details": "Transaction Details", "transaction.history": "Transaction History", "transaction.filter": "Filter Transactions", "transaction.search": "Search Transactions", "transaction.date.range": "Date Range", "transaction.amount.range": "Amount Range", "transaction.export": "Export Transactions", "transaction.import": "Import Transactions", "transaction.batch.edit": "<PERSON>ch Edit", "transaction.batch.delete": "<PERSON><PERSON> Delete", "transaction.selected": "Selected", "transaction.total.amount": "Total Amount", "transaction.record.deleted": "Transaction record deleted", "transaction.list.empty.title": "No transaction records", "transaction.list.empty.description": "Start recording your first transaction!", "transaction.list.selected": "Selected {{count}} items", "transaction.list.batch.edit": "<PERSON>ch Edit", "transaction.list.batch.delete": "<PERSON><PERSON> Delete", "transaction.list.header.records": "Record Details", "transaction.list.header.transactions": "Transaction Details", "transaction.list.pagination.info": "Page {{current}} of {{total}}", "transaction.delete.balance": "Delete Balance Record", "transaction.delete.transaction": "Delete Transaction", "transaction.delete.balance.title": "Delete Balance Record", "transaction.delete.balance.message": "Are you sure you want to delete this balance record? This action cannot be undone.", "transaction.delete.transaction.title": "Delete Transaction", "transaction.delete.transaction.message": "Are you sure you want to delete this transaction? This action cannot be undone.", "transaction.delete.batch.title": "Batch Delete Confirmation", "transaction.delete.batch.message": "Are you sure you want to delete the selected {{count}} records? This action cannot be undone.", "transaction.delete.batch.success": "Successfully deleted {{count}} records", "transaction.delete.batch.partial.error": "Failed to delete {{failed}}/{{total}} records, partial deletion unsuccessful", "batch.process.success": "Batch processing successful", "batch.process.success.detail": "Successfully processed {{count}} transaction records", "batch.delete.success": "Successfully deleted {{count}} records", "batch.delete.partial": "Deleted {{success}}/{{total}} records, some deletions failed", "transaction.search.placeholder": "Search description or notes...", "transaction.search.searching": "Searching...", "transaction.stats.total.income": "Total Income", "transaction.stats.total.expense": "Total Expense", "transaction.stats.net.flow": "Net Flow", "transaction.stats.this.month.net": "This Month Net", "transaction.stats.count.transactions": "{{count}} transactions", "transaction.stats.total.records": "Total {{count}} records", "transaction.stats.vs.last.month": "vs last month", "transaction.filter.tags": "Filter by Tags", "transaction.filter.tags.placeholder": "Select tags...", "transaction.filter.tags.all": "All Tags", "transaction.filter.tags.selected": "{{count}} tags selected", "transaction.filter.tags.clear": "Clear tag filters", "transaction.filter.no.tags": "No tags available", "transaction.filter.basic": "Basic Filters", "transaction.filter.date.range": "Date Range", "transaction.filter.quick.date": "Quick Date Selection", "transaction.form.description": "Please fill in transaction details", "transaction.description.placeholder": "Please enter {{type}} description...", "transaction.notes": "Notes", "transaction.notes.placeholder": "Add notes...", "transaction.update": "Update Transaction", "transaction.quick.record.title": "Quick Record {{type}}", "transaction.quick.income": "Income", "transaction.quick.expense": "Expense", "transaction.quick.transaction": "{{type}} Transaction", "transaction.quick.select.account.help": "Please select {{type}} account and fill in transaction information", "transaction.quick.account.label": "{{type}} Account", "transaction.quick.account.help": "Select account to record {{type}}", "transaction.quick.selected.account": "Selected Account:", "transaction.quick.category": "Category:", "transaction.quick.currency": "<PERSON><PERSON><PERSON><PERSON>", "transaction.quick.amount.label": "{{type}} Amount", "transaction.quick.date.label": "Transaction Date", "transaction.quick.description.label": "Transaction Description", "transaction.quick.description.placeholder": "Please enter {{type}} description...", "transaction.quick.tags.label": "Tags (Optional)", "transaction.quick.tags.create": "Create New Tag", "transaction.quick.notes.label": "Notes (Optional)", "transaction.quick.notes.placeholder": "Add notes...", "transaction.quick.cancel": "Cancel", "transaction.quick.add": "Add {{type}}", "transaction.quick.select.account.error": "Please select account", "transaction.quick.select.valid.account.error": "Please select valid account", "transaction.quick.create.failed": "Failed to create transaction", "transaction.quick.tag.create.success": "Created successfully", "transaction.quick.tag.added.success": "Tag created and added to current transaction", "transaction.create.failed": "Transaction creation failed", "transaction.update.failed": "Transaction update failed", "transaction.title": "Transaction", "transaction.notes.optional": "Notes (Optional)", "transaction.not.found": "Transaction record not found", "transaction.get.failed": "Failed to get transaction record"}