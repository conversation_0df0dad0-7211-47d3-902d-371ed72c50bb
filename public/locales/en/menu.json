{"menu.reports.description": "View detailed financial data", "menu.settings.description": "Manage accounts and preferences", "menu.logout.description": "Safely logout from current account", "menu.category.add.subcategory": "Add Subcategory", "menu.category.add.subcategory.description": "Create a new subcategory under this category", "menu.category.add.account": "Add {{type}} Account", "menu.category.add.account.description": "Create a new {{type}} account under this category", "menu.category.rename": "Rename Category", "menu.category.rename.description": "Modify category name", "menu.category.move": "Move Category", "menu.category.move.description": "Move category to another location", "menu.category.settings": "Category Settings", "menu.category.settings.description": "Configure category properties and permissions", "menu.category.delete": "Delete Category", "menu.category.delete.description": "Permanently delete this category and its sub-items", "menu.category.info.description": "{{dataType}} • {{description}}", "menu.account.view.details": "View Details", "menu.account.view.details.description": "View account details and transaction history", "menu.account.update.balance": "Update Balance", "menu.account.update.balance.description": "Adjust {{type}} account balance", "menu.account.add.transaction": "Add Transaction", "menu.account.add.transaction.description": "Record {{type}} transaction", "menu.account.rename": "<PERSON><PERSON> Account", "menu.account.rename.description": "Modify account name and description", "menu.account.move": "Move Category", "menu.account.move.description": "Move account to another category", "menu.account.settings": "Account <PERSON><PERSON>", "menu.account.settings.description": "Configure account properties and permissions", "menu.account.delete": "Delete Account", "menu.account.delete.description": "Permanently delete this account and its data", "menu.type.asset": "<PERSON><PERSON>", "menu.type.liability": "Liability", "menu.type.income": "Income", "menu.type.expense": "Expense", "menu.type.category": "Category", "menu.type.account": "Account", "menu.data.type.stock": "Stock", "menu.data.type.flow": "Flow", "menu.data.type.stock.description": "Records asset/liability status", "menu.data.type.flow.description": "Records income/expense flow", "menu.badge.stock": "Stock", "menu.badge.flow": "Flow"}