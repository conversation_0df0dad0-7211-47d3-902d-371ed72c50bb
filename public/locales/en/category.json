{"category.name": "Category Name", "category.type": "Category Type", "category.parent": "Parent Category", "category.create": "Create Category", "category.edit": "Edit Category", "category.delete": "Delete Category", "category.details": "Category Details", "category.summary": "Category Summary", "category.accounts": "Accounts", "category.subcategories": "Subcategories", "category.total.balance": "Total Balance", "category.account.count": "Account Count", "category.no.accounts": "No accounts in this category", "category.add.account": "Add Account", "category.add.subcategory": "Add Subcategory", "category.stock.readonly.tip": "Stock data reflects asset/liability status at specific points in time, focus on net value change trends", "category.net.balance": "Net Balance", "category.net.cash.flow": "Net Cash Flow", "category.increase": "Increase", "category.decrease": "Decrease", "category.income": "Income", "category.expense": "Expense", "category.subcategory": "Subcategory", "category.monthly.balance.summary": "Monthly Account Balance Summary", "category.monthly.cash.flow.summary": "Monthly Cash Flow Summary", "category.trend.analysis": "Trend Analysis", "category.type.asset": "Asset Category", "category.type.liability": "Liability Category", "category.type.stock": "Stock", "category.type.stock.data": "Stock Data", "category.type.income": "Income Category", "category.type.expense": "Expense Category", "category.type.flow.data": "Flow Data", "category.type.not.set": "Category Type Not Set", "category.type.not.set.description": "Please set the correct account type for this category to get professional statistical analysis", "category.current.balance": "Current Balance", "category.current.net.value": "Current Net Value", "category.monthly.change": "Monthly Change", "category.yearly.change": "Yearly Change", "category.last.month": "Last Month", "category.year.start": "Year Start", "category.transaction.count": "records", "category.currency.distribution": "Currency Distribution", "category.stock.update.tip": "Stock categories recommend updating balances on account pages", "category.balance.change.records": "Balance History", "category.balance.change.description": "Records balance change history for all accounts in this category", "category.flow.add.tip": "Flow categories can add transaction records", "category.type.flow": "Flow", "category.cumulative.net.flow": "Cumulative Net Flow", "category.this.month.net.flow": "This Month Net Flow", "category.cumulative.flow": "Cumulative Flow", "category.year.to.date.total.flow": "Year-to-date Total Flow", "category.this.month.total.flow": "This Month Total Flow", "category.this.year.total.flow": "This Year Total Flow", "category.last.year.total.flow": "Last Year Total Flow", "category.monthly.average.flow": "Monthly Average", "category.no.currency.data": "No currency data", "category.total.income": "Total Income", "category.total.expense": "Total Expense", "category.total.accounts": "Total Accounts", "category.flow.summary.description": "The following shows monthly flow data for subcategories and accounts. If no transactions this month, cumulative flow is displayed.", "category.all": "All Categories", "category.top.add": "Add Top-Level Category", "category.top.what.title": "What is a Top-Level Category?", "category.top.what.description": "Top-level categories are the foundational framework for financial management, used to distinguish different types of financial data. Each top-level category has a specific account type that determines the functionality and statistical methods of its subordinate accounts.", "category.name.placeholder": "Enter category name, e.g.: Cash Assets, Fixed Expenses, etc.", "category.name.required": "Category name is required", "category.name.too.long": "Category name cannot exceed 50 characters", "category.type.required": "Please select account type", "category.type.select.placeholder": "Please select account type", "category.type.asset.description": "Asset - Stock concept (e.g.: cash, bank deposits, investments, etc.)", "category.type.liability.description": "Liability - Stock concept (e.g.: credit cards, loans, payables, etc.)", "category.type.income.description": "Income - Flow concept (e.g.: salary, bonuses, investment returns, etc.)", "category.type.expense.description": "Expense - Flow concept (e.g.: dining, transportation, shopping, etc.)", "category.type.asset.title": "Asset Category", "category.type.asset.detail": "Stock concept - Records the current value of assets you own", "category.type.asset.feature.balance": "Balance Management", "category.type.asset.feature.statistics": "Asset Statistics", "category.type.asset.feature.networth": "Net Worth Calculation", "category.type.asset.feature.tracking": "Value Change Tracking", "category.type.liability.title": "Liability Category", "category.type.liability.detail": "Stock concept - Records the current balance of debts you need to repay", "category.type.liability.feature.management": "Debt Management", "category.type.liability.feature.statistics": "Liability Statistics", "category.type.liability.feature.networth": "Net Worth Calculation", "category.type.liability.feature.repayment": "Repayment Plan Tracking", "category.type.income.title": "Income Category", "category.type.income.detail": "Flow concept - Records your various income sources and amounts", "category.type.income.feature.record": "Income Recording", "category.type.income.feature.cashflow": "Cash Flow Statistics", "category.type.income.feature.trend": "Income Trend Analysis", "category.type.income.feature.budget": "Budget Comparison", "category.type.expense.title": "Expense Category", "category.type.expense.detail": "Flow concept - Records your various expenses and consumption", "category.type.expense.feature.record": "Expense Recording", "category.type.expense.feature.cashflow": "Cash Flow Statistics", "category.type.expense.feature.trend": "Expense Trend Analysis", "category.type.expense.feature.control": "Budget Control", "category.examples.title": "Common Examples", "category.examples.asset.cash": "Cash Assets: cash, bank deposits, Alipay, WeChat wallet", "category.examples.asset.investment": "Investment Assets: stocks, funds, bonds, financial products", "category.examples.asset.fixed": "Fixed Assets: real estate, vehicles, equipment, furniture", "category.examples.liability.credit": "Credit Liabilities: credit cards, <PERSON><PERSON>i, Baitiao", "category.examples.liability.loan": "Loan Liabilities: mortgages, car loans, consumer loans", "category.examples.liability.other": "Other Liabilities: payables, borrowings", "category.examples.income.work": "Work Income: salary, bonuses, commissions, allowances", "category.examples.income.investment": "Investment Income: dividends, interest, rental income", "category.examples.income.other": "Other Income: part-time, side business, gifts", "category.examples.expense.living": "Living Expenses: dining, transportation, shopping, entertainment", "category.examples.expense.fixed": "Fixed Expenses: rent, utilities, insurance, communications", "category.examples.expense.other": "Other Expenses: medical, education, travel, gifts", "category.creating": "Creating...", "category.settings": "Category Settings", "category.settings.basic.info": "Basic Information", "category.settings.level": "Category Level", "category.settings.top.level": "Top-Level Category", "category.settings.sort.order": "Sort Order", "category.settings.sort.placeholder": "Lower numbers appear first", "category.settings.top.level.description": "As a top-level category, you can set the account type for this category. All subcategories will automatically inherit this type.", "category.settings.subcategory.description": "As a subcategory, this category's account type is determined by its parent category and cannot be set independently.", "category.settings.inherited.type": "Inherited Account Type", "category.settings.parent.type.warning": "Parent category has not set an account type yet. It is recommended to set the parent category's account type first.", "category.settings.statistics.method": "Statistical Method Description", "category.settings.stock.statistics": "Stock Data Statistics", "category.settings.stock.balance.point": "Shows balance as of a specific point in time", "category.settings.stock.net.worth": "Calculates net worth (assets - liabilities)", "category.settings.stock.balance.sheet": "Suitable for balance sheet analysis", "category.settings.flow.statistics": "Flow Data Statistics", "category.settings.flow.period.amount": "Shows cumulative amount within a specific time period", "category.settings.flow.trend.analysis": "Analyzes income/expense trends and cash flow", "category.settings.flow.cash.flow.statement": "Suitable for cash flow statement analysis", "category.settings.asset.description": "Cash, bank deposits, investments, real estate, etc. (stock data)", "category.settings.liability.description": "Credit cards, loans, payables, etc. (stock data)", "category.settings.income.description": "Salary, investment returns, other income, etc. (flow data)", "category.settings.expense.description": "Living expenses, entertainment, transportation, etc. (flow data)", "category.settings.saving": "Saving...", "category.settings.save": "Save Settings", "category.selector.no.categories": "No categories available", "category.rename.title": "Rename Category", "category.rename.placeholder": "Enter new category name", "category.delete.title": "Delete Category", "category.delete.message": "Are you sure you want to delete category \"{{name}}\"? This action cannot be undone.", "category.delete.confirm": "Delete", "category.delete.cancel": "Cancel", "category.move.title": "Move Category to Another Category", "category.add.subcategory.title": "Add Subcategory", "category.add.subcategory.placeholder": "Enter subcategory name", "category.add.subcategory.failed": "Failed to add subcategory", "category.validation.name.required": "Category name is required", "category.validation.name.too.long": "Category name cannot exceed 50 characters", "category.renamed": "Category renamed", "category.deleted": "Category \"{{name}}\" deleted", "category.moved": "Category moved", "category.subcategory.added": "Subcategory added", "category.settings.saved": "Category settings saved", "category.settings.save.failed": "Failed to save settings", "category.type.change.error": "Cannot change category type", "category.type.change.error.message": "This category contains accounts or transaction data, cannot safely change type", "category.type.change.checking": "Checking type change safety...", "category.type.change.safe": "Type can be changed safely", "category.type.change.warning": "Type change has risks", "category.type.change.danger": "Cannot change type", "category.type.change.accounts.count": "This category has {{count}} accounts", "category.type.change.transactions.count": "Related accounts have {{count}} transaction records", "category.type.change.balance.records.count": "Contains {{count}} balance adjustment records", "category.type.change.risk.description": "Changing account type will cause existing transaction data to mismatch with the new type, which may cause data inconsistency. It is recommended to clean up related data before changing the type.", "category.existing.top.level": "Existing Top-Level Categories", "category.name.duplicate.warning": "Category name already exists, please choose another name", "category.name.available": "Category name is available", "category.name.duplicate.error": "Category name \"{{name}}\" already exists, please use another name", "category.create.unknown.error": "An unknown error occurred while creating the category", "category.get.failed": "Failed to get categories", "category.not.found": "Category not found", "category.parent.not.found": "Parent category not found", "category.top.level.type.required": "Top-level category must specify account type", "category.create.success": "Category created successfully", "category.parent.invalid": "Parent category does not exist or is invalid", "category.name.already.exists": "This category name already exists", "category.top.level.cannot.move": "Top-level categories cannot be moved", "category.cannot.set.self.as.parent": "Category cannot set itself as parent", "category.type.change.not.allowed": "Cannot change category type: accounts or transaction data exist under this category, changing type would cause data inconsistency", "category.update.success": "Category updated successfully", "category.update.failed": "Failed to update category", "category.has.children.cannot.delete": "This category has subcategories and cannot be deleted", "category.has.accounts.cannot.delete": "This category has accounts and cannot be deleted", "category.delete.failed": "Failed to delete category", "category.cannot.move.to.descendant": "Cannot move category to its subcategory", "category.cannot.move.different.type": "Subcategories can only be moved within the same account type", "category.delete.success": "Category deleted successfully"}