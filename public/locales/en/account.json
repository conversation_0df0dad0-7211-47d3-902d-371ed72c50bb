{"account.balance": "Balance", "account.currency": "<PERSON><PERSON><PERSON><PERSON>", "account.description": "Description", "account.category": "Category", "account.type": "Type", "account.create": "Create Account", "account.edit": "Edit Account", "account.delete": "Delete Account", "account.name": "Account Name", "account.details": "Account Details", "account.summary": "Account Summary", "account.transactions": "Transactions", "account.balance.history": "Balance History", "account.current.balance": "Current Balance", "account.total.income": "Total Income", "account.total.expense": "Total Expense", "account.transaction.count": "Transaction Count", "account.last.transaction": "Last Transaction", "account.no.transactions": "No transactions yet", "account.add.transaction": "Add Transaction", "account.update.balance": "Update Balance", "account.view.all": "View All", "account.all": "All Accounts", "account.use.clear.option": "Please use \"Clear balance history and delete\" option.", "account.balance.history.cleared": "Balance history cleared", "account.balance.history.clear.failed": "Failed to clear balance history", "account.balance.record.deleted": "Balance record deleted", "account.stock.operation.tips": "Stock Account Operation Tips", "account.stock.operation.description": "{{type}} accounts are mainly managed through \"Update Balance\", records reflect account status at specific points in time. It is recommended to regularly reconcile with bank statements or investment account balances.", "account.balance.history.description": "Record historical changes in account balance updates", "account.total.records": "Total {{count}} records", "account.clear.records": "Clear Records", "account.clear.balance.records": "Clear Balance Records", "account.clear.transactions": "Clear Transaction Records", "account.transactions.cleared": "Transaction records cleared", "account.transactions.clear.failed": "Failed to clear transaction records", "account.flow.operation.tips": "Flow Account Operation Tips", "account.flow.operation.description": "{{type}} accounts record cash flow through \"Add Transaction\", each transaction reflects fund inflow or outflow for specific periods. It is recommended to record each income and expense detail in time.", "account.total.transactions": "Total {{count}} transactions", "account.transaction.description": "Record detailed {{type}} flow", "account.transaction.filtered.description": "Showing filtered transaction records", "account.transactions.change.description": "Records flow history for all accounts in this category", "account.transaction.count.value": "{{count}} transactions", "account.type.asset": "Asset Account", "account.type.liability": "Liability Account", "account.type.income": "Income Account", "account.type.expense": "Expense Account", "account.type.unknown": "Uncategorized Account", "account.type.asset.description": "Stock concept - Records current balance status of assets", "account.type.liability.description": "Stock concept - Records current balance status of liabilities", "account.type.income.description": "Flow concept - Records cumulative amount of income", "account.type.expense.description": "Flow concept - Records cumulative amount of expenses", "account.type.unknown.description": "Please set the correct account type for this account category", "account.feature.balance.update": "Balance Update", "account.feature.asset.stats": "Asset Statistics", "account.feature.liability.stats": "Liability Statistics", "account.feature.net.worth": "Net Worth Calculation", "account.feature.transaction.record": "Transaction Record", "account.feature.income.stats": "Income Statistics", "account.feature.expense.stats": "Expense Statistics", "account.feature.cash.flow": "Cash Flow Analysis", "account.color.preview": "Preview: {{color}}", "account.balance.current": "Current Balance", "account.balance.last.month": "Last Month Balance", "account.change.monthly": "Monthly Change", "account.change.yearly": "Yearly Change", "account.balance.year.start": "Year Start", "account.nature": "Account Nature", "account.type.asset.category": "<PERSON><PERSON>", "account.type.liability.category": "Liability", "account.data.type": "Data Type", "account.data.type.stock": "Stock Data", "account.data.type.flow": "Flow Data", "account.data.type.point.balance": "Point-in-time Balance", "account.data.type.period.flow": "Period Flow", "account.stock.data.description": "Stock data reflects asset/liability status at specific points in time, focusing on net worth change trends", "account.flow.data.description": "Flow data reflects cash movement over specific periods, focusing on income/expense patterns and trends", "account.total.amount": "Total Amount", "account.amount.this.month": "This Month", "account.amount.last.month": "Last Month", "account.average.monthly": "Monthly Avg", "account.average.12months": "12-Month Avg", "account.year.total": "Year Total", "account.comparison.yearly": "Yearly Comparison", "account.type.income.category": "Income", "account.type.expense.category": "Expense", "account.rename.title": "<PERSON><PERSON> Account", "account.rename.placeholder": "Enter new account name", "account.delete.title": "Delete Account", "account.delete.item.type": "Account", "account.validation.name.required": "Account name is required", "account.validation.name.too.long": "Account name cannot exceed 50 characters", "account.added": "Account added", "account.title": "Account", "account.create.error": "Failed to create account", "account.add.title": "Add {{type}} Account", "account.creating": "Creating...", "account.settings": "Account <PERSON><PERSON>", "account.settings.basic.info": "Basic Information", "account.settings.account.name": "Account Name", "account.settings.account.description": "Account Description", "account.settings.name.placeholder": "Enter account name", "account.settings.description.placeholder": "Enter account description (optional)", "account.settings.display.settings": "Display Settings", "account.settings.currency.settings": "<PERSON><PERSON><PERSON><PERSON>", "account.settings.currency": "Account <PERSON><PERSON><PERSON><PERSON>", "account.settings.currency.help": "Account currency cannot be changed once set and has transaction records", "account.settings.currency.info": "Once account currency is set, all transactions for this account will use this currency", "account.settings.currency.selected": "Selected Currency", "account.settings.currency.select": "Please select currency", "account.settings.saving": "Saving...", "account.settings.save": "Save Settings", "account.get.failed": "Failed to get accounts", "account.name.required": "Account name is required", "account.category.required": "Please select a category", "account.currency.required": "Please select account currency", "account.name.already.exists": "This account name already exists", "account.create.success": "Account created successfully", "account.create.failed": "Failed to create account", "account.delete.success": "Deleted successfully", "account.delete.success.message": "Account \"{{name}}\" has been deleted", "account.delete.failed": "Delete failed", "account.delete.network.error": "Network error, please try again later", "account.clear.success": "Cleared successfully", "account.clear.default.message": "Balance history has been cleared", "account.clear.failed": "Clear failed", "account.clear.network.error": "Network error, please try again later", "account.move.failed": "Move failed", "account.move.unknown.error": "Unknown error", "account.move.network.error": "Network error, please try again later", "account.more.actions": "More actions", "account.balance.related.data.message": "This account has balance adjustment records. Please clear related data before deletion.", "account.clear.balance.and.delete": "Clear balance history and delete", "account.move.to.category": "Move account to another category"}