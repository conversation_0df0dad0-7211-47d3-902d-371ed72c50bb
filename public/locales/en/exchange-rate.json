{"exchange.rate.alert.title": "Exchange Rates Need Setup", "exchange.rate.alert.description": "You have {{count}} currency pairs that need exchange rate setup to ensure data accuracy:", "exchange.rate.alert.more.pairs": "{{count}} more currency pairs...", "exchange.rate.alert.setup.now": "Setup Exchange Rates Now", "exchange.rate.alert.ignore": "Ignore for Now", "exchange.rate.alert.close": "Close", "exchange.rate.fetch.failed": "Failed to fetch missing exchange rates", "exchange.rate.management": "Exchange Rate Management", "exchange.rate.management.description": "Manage your currency exchange rate settings to ensure all statistics can be correctly converted to base currency", "exchange.rate.loading": "Loading exchange rate data...", "exchange.rate.base.currency": "Base Currency", "exchange.rate.missing.title": "Exchange Rates Needed", "exchange.rate.missing.description": "You have {{count}} currency pairs that need exchange rates for accurate calculations.", "exchange.rate.setup": "Setup Rate", "exchange.rate.count": "{{count}} rates configured", "exchange.rate.add": "Add Exchange Rate", "exchange.rate.edit": "Edit Exchange Rate", "exchange.rate.update": "Update Exchange Rate", "exchange.rate.form.incomplete": "Please fill in all required fields", "exchange.rate.invalid.rate": "Exchange rate must be a number greater than 0", "exchange.rate.same.currency": "Source and target currencies cannot be the same", "exchange.rate.from.currency": "From Currency", "exchange.rate.to.currency": "To <PERSON><PERSON><PERSON><PERSON>", "exchange.rate.from.currency.help": "The original currency to convert from", "exchange.rate.to.currency.help": "The target currency to convert to", "exchange.rate.rate": "Exchange Rate", "exchange.rate.rate.placeholder": "e.g: 7.2", "exchange.rate.rate.help": "1 unit of source currency = ? units of target currency", "exchange.rate.effective.date": "Effective Date", "exchange.rate.effective.date.help": "The date when the exchange rate takes effect", "exchange.rate.notes": "Notes", "exchange.rate.notes.placeholder": "Optional notes...", "exchange.rate.deleted": "Exchange rate deleted", "exchange.rate.empty.title": "No exchange rates configured", "exchange.rate.empty.description": "Click the \"Add Exchange Rate\" button above to start configuring exchange rates", "exchange.rate.list": "Exchange Rate List", "exchange.rate.currency.pair": "<PERSON><PERSON><PERSON><PERSON>", "exchange.rate.delete.confirm.title": "Confirm Delete", "exchange.rate.delete.confirm": "Confirm Delete", "exchange.rate.delete.confirm.message": "Are you sure you want to delete this exchange rate setting? This action cannot be undone.", "exchange.rate.auto.update": "Auto Update Exchange Rates", "exchange.rate.auto.update.description": "When enabled, the system will automatically fetch latest rates from Frankfurter API", "exchange.rate.manual.update": "Manual Update", "exchange.rate.manual.update.description": "Immediately fetch latest rates from Frankfurter API", "exchange.rate.last.update": "Last Updated", "exchange.rate.never.updated": "Never Updated", "exchange.rate.updating": "Updating exchange rates...", "exchange.rate.update.success": "Exchange rate updated successfully", "exchange.rate.update.failed": "Failed to update exchange rates", "exchange.rate.update.partial": "Some exchange rates updated successfully", "exchange.rate.auto.update.settings": "Auto Update Settings", "exchange.rate.source.frankfurter": "Data Source: Frankfurter API", "exchange.rate.base.currency.required": "Please set base currency first", "exchange.rate.no.currencies": "No available currencies found", "exchange.rate.input.rates": "Input Rates", "exchange.rate.auto.generated.rates": "Auto Generated Rates", "exchange.rate.type.user": "Manual Input", "exchange.rate.type.api": "API Update", "exchange.rate.type.auto": "Auto Generated", "exchange.rate.type": "Type", "exchange.rate.no.input.rates": "No input rates", "exchange.rate.no.auto.rates": "No auto generated rates", "exchange.rate.invalid.currency": "Invalid currency", "exchange.rate.created": "Exchange rate created successfully", "exchange.rate.updated": "Exchange rate updated successfully", "exchange.rate.create.success": "Exchange rate created successfully", "exchange.rate.create.failed": "Failed to create exchange rate", "exchange.rate.auto.update.enabled": "Auto update exchange rates enabled", "exchange.rate.auto.update.disabled": "Auto update exchange rates disabled", "exchange.rate.base.currency.setup.required": "Please set base currency in preferences first", "exchange.rate.update.partial.message": "Successfully updated {{updatedCount}} rates, {{errorCount}} failed", "exchange.rate.update.success.message": "Successfully updated {{updatedCount}} rates", "exchange.rate.update.skipped.message": ", skipped {{skippedCount}} unsupported currencies", "exchange.rate.update.general.failed": "Update failed", "exchange.rate.network.error": "Network error, please try again later", "exchange.rate.settings.update.failed": "Failed to update settings", "exchange.rate.api.currency.not.supported": "Base currency {{currencyCode}} does not support automatic exchange rate updates. Please check if the currency code is correct or enter exchange rates manually", "exchange.rate.api.service.unavailable": "Exchange rate service is temporarily unavailable, please try again later", "exchange.rate.api.error.with.code": "Failed to fetch exchange rate data (error code: {{statusCode}}), please try again later", "exchange.rate.network.connection.failed": "Network connection failed, please check your network connection and try again", "exchange.rate.api.fetch.failed": "Failed to fetch exchange rate data, please try again later"}