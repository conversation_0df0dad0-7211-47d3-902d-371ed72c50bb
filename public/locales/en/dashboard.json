{"dashboard.title": "Dashboard", "dashboard.welcome": "Welcome back, {{name}}! This is day {{days}} of your accounting journey.", "dashboard.financial.overview": "Financial Overview", "dashboard.api.data.note": "Using API data to ensure calculation accuracy", "dashboard.account.count": "Account Count", "dashboard.net.worth": "Net Worth", "dashboard.total.assets": "Total Assets", "dashboard.total.liabilities": "Total Liabilities", "dashboard.monthly.income": "Monthly Income", "dashboard.monthly.expense": "Monthly Expense", "dashboard.net.cash.flow": "Net Cash Flow", "dashboard.quick.actions": "Quick Actions", "dashboard.record.income": "Record Income", "dashboard.record.expense": "Record Expense", "dashboard.update.balance": "Update Balance", "dashboard.net.worth.card": "Net Worth", "dashboard.total.assets.card": "Total Assets", "dashboard.total.liabilities.card": "Total Liabilities", "dashboard.account.balances.card": "Account Balances", "dashboard.recent.activity.card": "Recent 7 Days Activity", "dashboard.view.all": "View All", "dashboard.no.account.data": "No account data", "dashboard.view.other.accounts": "View other {{count}} accounts", "dashboard.transactions.count": "{{count}} transactions", "dashboard.income": "Income", "dashboard.expense": "Expense", "dashboard.data.quality.score": "Data Quality Score", "dashboard.validation.errors": "Data Validation Errors", "dashboard.validation.warnings": "Data Validation Warnings", "dashboard.accounts.checked": "Accounts Checked", "dashboard.transactions.checked": "Transactions Checked", "dashboard.categories.without.type": "Without Type", "dashboard.invalid.transactions": "Invalid Transactions", "dashboard.business.logic.violations": "Logic Violations", "dashboard.chart.data.validation.failed": "Chart data validation failed: {{errors}}", "dashboard.chart.data.fetch.failed": "Failed to fetch chart data", "dashboard.network.error.charts": "Network error, unable to fetch chart data", "dashboard.no.chart.data": "No chart data available", "dashboard.no.summary.data": "No summary data available", "dashboard.add.transactions.first": "Please add some transaction records first", "dashboard.monthly.net.income": "Net Income", "dashboard.assets.minus.liabilities": "Assets - Liabilities", "dashboard.accounts.count": "{{count}} accounts", "dashboard.transaction.records": "Transaction Records", "dashboard.category.count": "Category Count", "dashboard.recent.30days": "Recent 30 days", "dashboard.income.label": "Income:", "dashboard.expense.label": "Expense:", "dashboard.financial.trend.analysis": "Financial Trend Analysis", "dashboard.net.worth.change.trend": "Net Worth Change Trend", "dashboard.monthly.cash.flow": "Monthly Cash Flow", "dashboard.add.accounts.transactions.first": "Please add accounts and transaction records to view net worth trends", "dashboard.sync.status": "Sync Status", "dashboard.system.update.status": "System Update Status", "sync.status.idle": "Idle", "sync.status.processing": "Processing", "sync.status.completed": "Completed", "sync.status.failed": "Failed", "sync.retry": "Retry", "sync.manual.trigger": "Manual Update", "sync.manual.triggered": "Manual Update Triggered", "sync.manual.triggered.message": "The system is processing update transactions...", "sync.manual.failed": "Manual Update Failed", "sync.manual.failed.message": "Please try again later or contact administrator", "sync.processing": "Updating", "sync.error": "Error", "sync.last.time": "Last Sync", "sync.processed.recurring": "Recurring Transactions", "sync.processed.loans": "Loan Contracts", "sync.processed.exchange.rates": "Exchange Rates", "sync.processed.summary": "Processed {{recurring}} recurring transactions, {{loans}} loan contracts", "sync.processed.summary.with.rates": "Processed {{recurring}} recurring transactions, {{loans}} loan contracts, {{exchangeRates}} exchange rate updates", "sync.future.data.generated": "Future data generated until {{until}}", "sync.processing.message": "Processing recurring transactions, loan contracts and exchange rates...", "sync.stages.title": "Processing Stages", "sync.stages.recurringTransactions": "Recurring Transactions", "sync.stages.loanContracts": "Loan Contracts", "sync.stages.exchangeRates": "Exchange Rates", "dashboard.accounts.unit": "accounts", "dashboard.currency.breakdown.title": "Breakdown", "dashboard.currency.breakdown.note": "Shows original and converted amounts by currency", "dashboard.debt.to.asset.ratio": "Debt-to-<PERSON><PERSON>"}