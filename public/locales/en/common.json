{"common.save": "Save", "common.saving": "Saving...", "common.cancel": "Cancel", "common.close": "Close", "common.delete": "Delete", "common.deleting": "Deleting...", "common.edit": "Edit", "common.add": "Add", "common.confirm": "Confirm", "common.loading": "Loading...", "common.redirecting": "Initializing...", "common.processing": "Processing...", "common.select": "Select", "common.initializing": "Initializing...", "common.preparing": "Preparing...", "common.loading.data": "Loading data...", "common.loading.page": "Loading page...", "common.loading.app": "Starting application...", "common.app.subtitle": "Personal Finance Management", "common.search": "Search", "common.filter": "Filter", "common.export": "Export", "common.import": "Import", "common.refresh": "Refresh", "common.back": "Back", "common.next": "Next", "common.previous": "Previous", "common.submit": "Submit", "common.submitting": "Submitting...", "common.reset": "Reset", "common.clear": "Clear", "common.records": "records", "common.clear.all": "Clear All Filters", "common.clear.filter": "Clear Filter", "common.confirm.clear": "Confirm Clear", "common.all": "All", "common.items": "items", "common.none": "None", "common.empty": "Empty", "common.other": "Other", "common.yes": "Yes", "common.no": "No", "common.ok": "OK", "common.error": "Error", "common.success": "Success", "common.warning": "Warning", "common.info": "Info", "error.unknown": "Unknown error", "common.switch.to": "Switch to", "common.new": "New", "common.delete.failed": "Delete failed", "common.actions": "Actions", "common.confirm.delete": "Confirm Delete", "common.total": "Total", "common.pagination.previous": "Previous", "common.pagination.next": "Next", "common.pagination.showing": "Showing {{start}} to {{end}} of {{total}} records", "common.date.today": "Today", "common.date.yesterday": "Yesterday", "common.date.days.ago": "{{days}} days ago", "common.date.tomorrow": "Tomorrow", "common.date.days.later": "{{days}} days later", "common.date.future": "Future", "common.retry": "Retry", "common.this.year": "This Year", "common.theme.light": "Light Mode", "common.theme.dark": "Dark Mode", "common.theme.system": "Follow System", "common.theme.light.short": "Light", "common.theme.dark.short": "Dark", "common.theme.system.short": "Auto", "common.theme.select": "Select Theme", "common.income": "Income", "common.expense": "Expense", "common.currency": "<PERSON><PERSON><PERSON><PERSON>", "common.save.changes": "Save Changes", "input.dialog.required": "Please enter content", "delete.confirm.message": "Are you sure you want to delete {{itemType}} \"{{itemName}}\"?", "delete.confirm.warning": "This action cannot be undone. Please proceed with caution.", "delete.clear.related.data": "Clear Related Data", "delete.clear.and.delete.warning": "Related data will be cleared first, then {{itemType}} will be deleted. This action cannot be undone.", "delete.confirm.clear.and.delete": "Confirm Clear and Delete", "delete.confirm": "Confirm Delete", "delete.password.prompt": "Please enter password to confirm deletion:", "delete.password.placeholder": "Enter password", "common.network.error": "Network error, please try again later", "common.no.data": "No data available", "common.more": "more", "transaction.notes": "Notes", "transaction.tags": "Tags", "transaction.tags.empty": "No tags available", "transaction.tag.recurring": "Recurring", "transaction.tag.loan": "Loan", "transaction.tag.loan.payment": "Payment", "transaction.tag.loan.principal": "Principal", "tag.create.new": "Create New Tag", "recurring.transaction.delete": "Delete Recurring Transaction", "confirm.delete.recurring.transaction.message": "Are you sure you want to delete this recurring transaction? This action cannot be undone.", "loan.contracts": "Loan Contracts", "loan.contract.create": "Create <PERSON><PERSON> Contract", "loan.contract.add.new": "Click to add a new loan contract", "loan.contract.limit.title": "Loan Contract Limit", "exchange.rate.auto.generated.reverse": "Auto-generated reverse rate, based on {fromCurrency}→{toCurrency}", "exchange.rate.auto.generated.transitive": "Auto-generated transitive rate, calculation path: {calculationPath}", "exchange.rate.create.success": "Exchange rate created successfully", "exchange.rate.update.success": "Exchange rate updated successfully", "exchange.rate.auto.generate.failed": "Failed to auto-regenerate exchange rates", "exchange.rate.auto.generate.partial.failed": "Auto-generation of exchange rates partially failed: {errors}", "exchange.rate.auto.generate.success": "Successfully auto-generated {count} exchange rate records", "exchange.rate.invalid.date.format": "Invalid date format", "exchange.rate.auto.generate.process.failed": "Failed to auto-generate exchange rates", "exchange.rate.transitive.generate.failed": "Failed to generate transitive rate: {error}", "exchange.rate.transitive.process.failed": "Transitive rate generation process failed: {error}", "exchange.rate.cleanup.failed": "Failed to cleanup auto-generated rates: {error}", "loan.contract.validation.failed": "Loan parameter validation failed: {errors}", "loan.contract.payment.day.invalid": "Payment day must be between 1-31", "loan.contract.currency.not.found": "Specified currency not found", "loan.contract.account.invalid": "Invalid account: must be a liability account", "loan.contract.account.cannot.change.with.payments": "Cannot change account when there are completed payments", "loan.contract.currency.cannot.change.with.payments": "Cannot change currency when there are completed payments", "loan.contract.not.found": "Loan contract not found", "loan.contract.generation.failed": "Loan contract {contractId} generation failed: {error}", "loan.contract.save.failed": "Failed to save loan contract", "loan.contract.create.failed": "Failed to create loan contract", "loan.contract.update.failed": "Failed to update loan contract", "loan.contract.delete.failed": "Failed to delete loan contract", "loan.contract.fetch.failed": "Failed to fetch loan contracts", "loan.contract.unauthorized": "Unauthorized access", "loan.contract.missing.fields": "Missing required fields", "loan.contract.amount.invalid": "Loan amount must be greater than 0", "loan.contract.rate.invalid": "Interest rate must be between 0-100%", "loan.contract.periods.invalid": "Total periods must be a positive integer", "loan.contract.payment.day.range.invalid": "Payment day must be between 1-31", "loan.contract.account.has.existing": "This account already has a loan contract, only one loan contract per account is allowed", "loan.contract.not.exists": "Loan contract does not exist", "loan.contract.periods.too.small": "New total periods must be greater than the maximum completed period ({maxPeriod})", "loan.contract.deleted": "Loan contract deleted", "loan.contract.payment.account.invalid": "Payment account does not exist, is not an expense account, or currency does not match", "loan.contract.no.payments.to.reset": "No payment records found to reset", "loan.contract.no.completed.payments": "No completed payment records to reset", "balance.change.amount.pattern": "Balance change: {amount}", "loan.contract.template.default.description": "{contractName} - Period {period} {type}", "loan.contract.template.default.notes": "Loan contract: {contractName}", "loan.contract.template.balance.adjustment": "Account balance adjustment - {contractName} Period {period} principal payment", "loan.contract.template.balance.notes": "Loan contract: {contractName}, remaining principal: {remainingBalance}", "loan.contract.limit.message": "An account can only have one loan contract", "loan.contract.updated": "Loan contract updated", "loan.contract.created": "Loan contract created", "loan.type.principal": "Principal", "loan.type.interest": "Interest", "loan.type.balance.update": "Balance Update", "loan.validation.rate.too.high": "Interest rate exceeds 30%, please confirm if correct", "loan.validation.rate.too.low": "Interest rate below 1%, please confirm if this is a preferential rate", "loan.validation.periods.too.long": "Loan period exceeds 30 years, please confirm if reasonable", "loan.validation.periods.too.short": "Consider other financing options for short-term loans", "loan.validation.amount.too.large": "Large loan amount, please confirm risk tolerance", "loan.validation.start.date.too.old": "Loan start date is more than 30 days ago, please confirm if this is a historical loan", "loan.validation.payment.day.month.end": "Setting payment day at month-end may cause issues in some months", "loan.validation.payment.day.suggestion": "Recommend setting payment day between 1-28", "loan.validation.interest.only.too.long": "Interest-only loan period is long, please confirm final principal repayment plan", "loan.validation.data.format.error": "Please check if input data format is correct", "loan.validation.unknown.error": "Unknown error occurred during validation", "loan.validation.account.not.found": "Specified loan account does not exist", "loan.validation.account.not.liability": "Loan account must be liability type", "loan.validation.payment.account.not.found": "Specified payment account does not exist", "loan.validation.payment.account.not.expense": "Payment account must be expense type", "loan.validation.currency.mismatch": "Loan account and payment account must have the same currency", "loan.validation.payment.account.suggestion": "Recommend setting payment account for automatic payment transaction generation", "loan.validation.account.association.error": "Error occurred while validating account association", "loan.validation.duplicate.contract": "An active loan contract already exists for this account", "loan.validation.duplicate.check.error": "Unable to check duplicate contracts, please verify manually", "loan.payment.reset.unauthorized": "Unauthorized access", "loan.payment.reset.select.records": "Please select payment records to reset", "loan.payment.reset.failed": "Failed to reset payment records", "loan.contracts.empty.title": "No Loan Contracts", "loan.contracts.empty.description": "Click to add your first loan contract", "loan.interest.rate": "Interest Rate", "loan.repayment.type": "Repayment Type", "loan.monthly.payment": "Monthly Payment", "loan.next.payment": "Next Payment", "loan.progress": "Progress", "loan.periods": "periods", "loan.status.active": "Active", "loan.status.inactive": "Inactive", "loan.repayment.equal.payment": "Equal Payment", "loan.repayment.equal.principal": "Equal Principal", "loan.repayment.interest.only": "Interest Only", "loan.amount": "<PERSON><PERSON>", "loan.total.periods": "Total Periods", "loan.payment.settings": "Payment Settings", "loan.payment.day": "Monthly Payment Day", "loan.payment.day.description": "Day of month for payment (1-31)", "loan.payment.account": "Payment Account", "loan.payment.account.select": "Select Payment Account", "loan.payment.account.description": "Select expense account for automatic payment, currency must match loan account", "loan.transaction.template": "Transaction Template", "loan.category.settings": "Category Settings", "loan.principal.category": "Principal Payment Category", "loan.interest.category": "Interest Expense Category", "category.select": "Select Category", "loan.payment.day.monthly": "{day}th of each month", "loan.start.date": "Start Date", "loan.payment.account.no.available": "No payment accounts available", "tags.no.available": "No tags available", "category.no.available": "No categories available", "mortgage.loan.create": "Create Mortgage Loan", "mortgage.loan.create.description": "Create a contract for home mortgage", "loan.contract.create.description": "Create a general loan contract", "mortgage.loan.edit": "Edit Mortgage Loan", "mortgage.loan.name": "Mortgage Loan Name", "mortgage.loan.amount": "<PERSON><PERSON>", "mortgage.loan.interest.rate": "Annual Interest Rate", "mortgage.loan.total.periods": "<PERSON>an <PERSON>", "mortgage.loan.periods.10years": "10 Years (120 periods)", "mortgage.loan.periods.15years": "15 Years (180 periods)", "mortgage.loan.periods.20years": "20 Years (240 periods)", "mortgage.loan.periods.25years": "25 Years (300 periods)", "mortgage.loan.periods.30years": "30 Years (360 periods)", "mortgage.loan.repayment.type": "Repayment Type", "mortgage.loan.repayment.equal.payment": "Equal Payment", "mortgage.loan.repayment.equal.principal": "Equal Principal", "mortgage.loan.payment.settings": "Payment Settings", "mortgage.loan.start.date": "Start Date", "mortgage.loan.payment.day": "Monthly Payment Day", "mortgage.loan.payment.day.description": "Day of the month for payment (1-31)", "mortgage.loan.payment.account": "Payment Account", "mortgage.loan.payment.account.select": "Select Payment Account", "mortgage.loan.payment.account.description": "Select expense account for payments (optional)", "mortgage.loan.payment.account.no.available": "No payment accounts available", "mortgage.loan.transaction.template": "Transaction Template", "mortgage.loan.cancel": "Cancel", "mortgage.loan.save": "Save", "loan.payment.history": "Payment History", "loan.payment.history.view": "View Payment History", "loan.payment.history.empty.title": "No Payment Records", "loan.payment.history.empty.description": "No payment records yet", "loan.payment.period": "Period", "loan.payment.period.number": "Period {period}", "loan.payment.date": "Payment Date", "loan.payment.principal": "Principal", "loan.payment.interest": "Interest", "loan.payment.total": "Total Amount", "loan.payment.remaining.balance": "Remaining Balance", "loan.payment.summary": "Payment Summary", "loan.payment.total.periods": "Periods Paid", "loan.payment.total.paid": "Total Paid", "loan.payment.current.balance": "Current Balance", "loan.contract.create.title": "Create <PERSON><PERSON> Contract", "loan.payment.status.completed": "Completed", "loan.payment.status.pending": "Pending", "loan.payment.status.failed": "Failed", "transaction.quick.tag.create.success": "Tag created successfully", "transaction.quick.tag.added.success": "New tag added to selected list", "preferences.data.generation.settings": "Data Generation Settings", "preferences.data.generation.description": "Configure future data generation for recurring transactions and loan payments", "preferences.future.data.days": "Days Ahead", "preferences.future.data.days.help": "The system will generate recurring transactions and loan payment records for the specified number of days. Setting 0 days means no future records will be generated, only records up to today; setting 1 day will generate tomorrow's records; setting 7 days will generate records for the next 7 days", "preferences.future.data.days.disabled": "No future records", "common.days": "days", "common.server.error": "Internal server error"}