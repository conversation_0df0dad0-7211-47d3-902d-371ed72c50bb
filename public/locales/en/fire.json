{"fire.title": "FIRE Journey", "fire.subtitle": "Grounded in Reality, Insight, Foresight, Control", "fire.description": "Transform abstract financial freedom goals into a visual, interactive, dynamic life dashboard with instant feedback", "fire.reality.snapshot.title": "Reality Snapshot", "fire.reality.snapshot.subtitle": "The following data is the foundation for your FIRE predictions", "fire.reality.snapshot.past12months.expenses": "Past 12 Months Total Expenses", "fire.reality.snapshot.monthly.average": "Monthly Average", "fire.reality.snapshot.current.net.worth": "Current Net Worth", "fire.reality.snapshot.historical.return": "Historical Annual Return", "fire.reality.snapshot.monthly.net.investment": "Monthly Net Investment", "fire.reality.snapshot.calibrate": "Calibrate", "fire.reality.snapshot.p1.source": "P1 Source", "fire.reality.snapshot.p3.source": "P3 Source", "fire.reality.snapshot.p4.source": "P4 Source", "fire.reality.snapshot.expenses.source": "This data is used to preset \"Retirement Annual Expenses\"", "fire.reality.snapshot.networth.source": "This data is used to preset \"Current Investable Assets\"", "fire.reality.snapshot.return.source": "This data is used to preset \"Expected Annual Return\"", "fire.reality.snapshot.investment.source": "This data is used to preset \"Monthly Net Investment\"", "fire.reality.snapshot.cagr.period": "Calculation Period", "fire.reality.snapshot.cagr.to.now": "to now", "fire.reality.snapshot.cagr.history": "History", "fire.reality.snapshot.cagr.initial.net.worth": "Initial Net Worth", "fire.reality.snapshot.cagr.investment.growth": "Investment Growth", "fire.reality.snapshot.cagr.description": "Based on net worth appreciation, cash flows excluded", "fire.north.star.title": "The North Star", "fire.north.star.subtitle": "The most intuitive and impactful way to show you the four most important numbers about your financial freedom", "fire.north.star.fire.target": "FIRE Target Amount", "fire.north.star.fire.target.description": "You need this much in assets to live off passive income.", "fire.north.star.fire.date": "Projected FIRE Date", "fire.north.star.fire.date.format": "{{month}}/{{year}}", "fire.north.star.fire.date.description": "(approximately {{years}} years {{months}} months from now)", "fire.north.star.fire.date.invalid": "Cannot Calculate", "fire.north.star.fire.date.invalid.description": "Please check your investment parameter settings", "fire.north.star.current.progress": "Current Progress", "fire.north.star.current.progress.description": "Current investable assets: {{amount}}", "fire.north.star.retirement.income": "Retirement \"Salary\"", "fire.north.star.retirement.income.description": "By then, your assets will provide you monthly...", "fire.journey.title": "The Journey", "fire.journey.subtitle": "Your wealth growth journey visualization", "fire.journey.description": "Transform the abstract wealth growth process into a visual movie that you can witness with your own eyes, full of emotional connection", "fire.journey.asset.growth": "Asset Growth Curve", "fire.journey.fire.target.line": "FIRE Target Line", "fire.journey.fire.point": "FIRE Achievement Point", "fire.journey.tooltip": "{{date}}: Assets projected to reach {{amount}}", "fire.cockpit.title": "The Cockpit", "fire.cockpit.subtitle": "Adjust parameters to control your financial future", "fire.cockpit.magic.description": "🎯 This is where the magic happens - with simple drag and drop, you can personally \"direct\" your own future", "fire.cockpit.retirement.expenses": "Retirement Annual Expenses", "fire.cockpit.retirement.expenses.description": "What kind of life do you want in retirement? This determines your total goal.", "fire.cockpit.safe.withdrawal.rate": "Safe Withdrawal Rate (SWR)", "fire.cockpit.safe.withdrawal.rate.description": "How much do you plan to withdraw annually from your assets? (Range 1.0%-10.0%, classic theory is 4%)", "fire.cockpit.current.investable.assets": "Current Investable Assets", "fire.cockpit.current.investable.assets.description": "How much initial capital do you have for achieving your FIRE goal?", "fire.cockpit.expected.annual.return": "Expected Annual Return", "fire.cockpit.expected.annual.return.description": "What annual return do you expect from your future investment portfolio?", "fire.cockpit.monthly.investment": "Monthly Net Investment", "fire.cockpit.monthly.investment.description": "How much money do you plan to invest monthly toward this goal?", "fire.cockpit.configure": "Configure", "fire.calculation.fire.target": "FIRE Target Amount", "fire.calculation.fire.date": "Projected FIRE Date", "fire.calculation.current.progress": "Current Progress", "fire.calculation.monthly.income": "Retirement Monthly Income", "fire.error.no.data": "Insufficient data for FIRE calculations", "fire.error.invalid.settings": "Invalid FIRE settings, please check your configuration", "fire.error.calculation.failed": "FIRE calculation failed, please try again later", "fire.help.swr.title": "What is Safe Withdrawal Rate?", "fire.help.swr.description": "Safe Withdrawal Rate (SWR) is the percentage of funds that can be withdrawn annually from an investment portfolio without depleting the principal. The 4% rule is a classic theory based on historical data.", "fire.status.enabled": "FIRE feature is enabled", "fire.status.disabled": "FIRE feature is not enabled", "fire.status.enable.instruction": "Please enable FIRE feature in settings to view this page.", "fire.units.years": "years", "fire.units.months": "months", "fire.units.per.year": "/year", "fire.units.per.month": "/month"}