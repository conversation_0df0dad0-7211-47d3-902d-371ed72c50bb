{"tag.management": "Tag Management", "tag.management.description": "Manage your transaction tags for better categorization and filtering of transaction records", "tag.add": "Add Tag", "tag.edit": "Edit Tag", "tag.create": "Create Tag", "tag.update": "Update Tag", "tag.name": "Tag Name", "tag.color": "Tag Color", "tag.name.placeholder": "Enter tag name", "tag.name.required": "Tag name cannot be empty", "tag.color.selected": "Selected", "tag.usage.count": "{{count}} transactions using", "tag.empty.title": "No tags yet", "tag.empty.description": "Start creating your first tag to categorize transaction records", "tag.created": "Tag created", "tag.updated": "Tag updated", "tag.deleted": "Tag deleted", "tag.delete.title": "Delete Tag", "tag.delete.confirm": "Confirm Delete", "tag.delete.confirm.message": "Are you sure you want to delete tag \"{{name}}\"? This action cannot be undone.", "tag.delete.in.use.message": "Tag \"{{name}}\" is being used by {{count}} transactions and cannot be deleted. Please remove this tag from related transactions first.", "tag.delete.failed": "Failed to delete tag", "tag.create.success": "Created successfully", "tag.update.success": "Updated successfully", "tag.create.failed": "Creation failed", "tag.update.failed": "Update failed", "tag.operation.failed": "Operation failed", "tag.created.and.added": "Tag created and added to current transaction", "tag.label.optional": "Tags (Optional)", "tag.create.new": "Create New Tag", "tag.get.failed": "Failed to get tags", "tag.name.too.long": "Tag name cannot exceed 50 characters", "tag.color.format.invalid": "Invalid color format", "tag.name.already.exists": "This tag name already exists"}