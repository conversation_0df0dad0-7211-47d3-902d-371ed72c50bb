{"category.name": "分类名称", "category.type": "分类类型", "category.parent": "父分类", "category.create": "创建分类", "category.edit": "编辑分类", "category.delete": "删除分类", "category.details": "分类详情", "category.summary": "分类摘要", "category.accounts": "账户", "category.subcategories": "子分类", "category.total.balance": "总余额", "category.account.count": "账户数量", "category.no.accounts": "此分类下暂无账户", "category.add.account": "添加账户", "category.add.subcategory": "添加子分类", "category.stock.readonly.tip": "存量数据反映特定时点的资产/负债状况，关注净值变化趋势", "category.net.balance": "净余额", "category.net.cash.flow": "净收支", "category.increase": "增加", "category.decrease": "减少", "category.income": "收入", "category.expense": "支出", "category.subcategory": "子分类", "category.monthly.balance.summary": "月度账户余额汇总", "category.monthly.cash.flow.summary": "月度收支汇总", "category.trend.analysis": "趋势分析", "category.type.asset": "资产分类", "category.type.liability": "负债分类", "category.type.stock": "存量", "category.type.stock.data": "存量数据", "category.type.income": "收入分类", "category.type.expense": "支出分类", "category.type.flow.data": "流量数据", "category.type.not.set": "分类类型未设置", "category.type.not.set.description": "请为该分类设置正确的账户类型以获得专业的统计分析", "category.current.balance": "当前余额", "category.current.net.value": "当前净值", "category.monthly.change": "月度变化", "category.yearly.change": "年度变化", "category.last.month": "上月", "category.year.start": "年初", "category.transaction.count": "笔记录", "category.currency.distribution": "币种分布", "category.stock.update.tip": "存量类分类建议在账户页面进行余额更新", "category.balance.change.records": "余额历史", "category.balance.change.description": "记录该分类下所有账户的余额变化历史", "category.flow.add.tip": "流量类分类可以添加交易记录", "category.type.flow": "流量", "category.cumulative.net.flow": "累计净流量", "category.this.month.net.flow": "本月净流量", "category.cumulative.flow": "累计流量", "category.year.to.date.total.flow": "年初至今总流量", "category.this.month.total.flow": "本月总流量", "category.this.year.total.flow": "今年总流量", "category.last.year.total.flow": "上年总流量", "category.monthly.average.flow": "月均", "category.no.currency.data": "暂无币种数据", "category.total.income": "总收入", "category.total.expense": "总支出", "category.total.accounts": "总账户数", "category.flow.summary.description": "以下显示各子分类和账户的本月流量数据，如本月无交易则显示累计流量", "category.all": "全部分类", "category.top.add": "添加顶级分类", "category.top.what.title": "什么是顶级分类？", "category.top.what.description": "顶级分类是财务管理的基础框架，用于区分不同性质的财务数据。每个顶级分类都有特定的账户类型，决定了其下属账户的功能和统计方式。", "category.name.placeholder": "请输入分类名称，如：现金资产、固定支出等", "category.name.required": "分类名称不能为空", "category.name.too.long": "分类名称不能超过50个字符", "category.type.required": "请选择账户类型", "category.type.select.placeholder": "请选择账户类型", "category.type.asset.description": "资产类 - 存量概念（如：现金、银行存款、投资等）", "category.type.liability.description": "负债类 - 存量概念（如：信用卡、贷款、应付款等）", "category.type.income.description": "收入类 - 流量概念（如：工资、奖金、投资收益等）", "category.type.expense.description": "支出类 - 流量概念（如：餐饮、交通、购物等）", "category.type.asset.title": "资产类分类", "category.type.asset.detail": "存量概念 - 记录您拥有的资产的当前价值", "category.type.asset.feature.balance": "余额管理", "category.type.asset.feature.statistics": "资产统计", "category.type.asset.feature.networth": "净资产计算", "category.type.asset.feature.tracking": "价值变动追踪", "category.type.liability.title": "负债类分类", "category.type.liability.detail": "存量概念 - 记录您需要偿还的债务的当前余额", "category.type.liability.feature.management": "债务管理", "category.type.liability.feature.statistics": "负债统计", "category.type.liability.feature.networth": "净资产计算", "category.type.liability.feature.repayment": "还款计划追踪", "category.type.income.title": "收入类分类", "category.type.income.detail": "流量概念 - 记录您的各种收入来源和金额", "category.type.income.feature.record": "收入记录", "category.type.income.feature.cashflow": "现金流统计", "category.type.income.feature.trend": "收入趋势分析", "category.type.income.feature.budget": "预算对比", "category.type.expense.title": "支出类分类", "category.type.expense.detail": "流量概念 - 记录您的各种支出和消费", "category.type.expense.feature.record": "支出记录", "category.type.expense.feature.cashflow": "现金流统计", "category.type.expense.feature.trend": "支出趋势分析", "category.type.expense.feature.control": "预算控制", "category.examples.title": "常见示例", "category.examples.asset.cash": "现金资产：现金、银行存款、支付宝、微信钱包", "category.examples.asset.investment": "投资资产：股票、基金、债券、理财产品", "category.examples.asset.fixed": "固定资产：房产、车辆、设备、家具", "category.examples.liability.credit": "信用负债：信用卡、花呗、白条", "category.examples.liability.loan": "贷款负债：房贷、车贷、消费贷", "category.examples.liability.other": "其他负债：应付款、借款", "category.examples.income.work": "工作收入：工资、奖金、提成、津贴", "category.examples.income.investment": "投资收入：股息、利息、租金收入", "category.examples.income.other": "其他收入：兼职、副业、礼金", "category.examples.expense.living": "生活支出：餐饮、交通、购物、娱乐", "category.examples.expense.fixed": "固定支出：房租、水电、保险、通讯", "category.examples.expense.other": "其他支出：医疗、教育、旅行、礼品", "category.creating": "创建中...", "category.settings": "分类设置", "category.settings.basic.info": "基本信息", "category.settings.level": "分类层级", "category.settings.top.level": "顶级分类", "category.settings.sort.order": "排序顺序", "category.settings.sort.placeholder": "数字越小排序越靠前", "category.settings.top.level.description": "作为顶级分类，您可以设置此分类的账户类型。所有子分类将自动继承此类型。", "category.settings.subcategory.description": "作为子分类，此分类的账户类型由父分类决定，无法单独设置。", "category.settings.inherited.type": "继承的账户类型", "category.settings.parent.type.warning": "父分类尚未设置账户类型，建议先设置父分类的账户类型。", "category.settings.statistics.method": "统计方法说明", "category.settings.stock.statistics": "存量数据统计", "category.settings.stock.balance.point": "显示截止到特定时间点的余额", "category.settings.stock.net.worth": "计算净资产（资产-负债）", "category.settings.stock.balance.sheet": "适用于资产负债表分析", "category.settings.flow.statistics": "流量数据统计", "category.settings.flow.period.amount": "显示特定时间段内的累计金额", "category.settings.flow.trend.analysis": "分析收支趋势和现金流", "category.settings.flow.cash.flow.statement": "适用于现金流量表分析", "category.settings.asset.description": "现金、银行存款、投资、房产等（存量数据）", "category.settings.liability.description": "信用卡、贷款、应付款等（存量数据）", "category.settings.income.description": "工资、投资收益、其他收入等（流量数据）", "category.settings.expense.description": "生活费、娱乐、交通等（流量数据）", "category.settings.saving": "保存中...", "category.settings.save": "保存设置", "category.selector.no.categories": "暂无分类", "category.rename.title": "重命名分类", "category.rename.placeholder": "请输入新的分类名称", "category.delete.title": "删除分类", "category.delete.message": "确定要删除分类\"{{name}}\"吗？此操作不可撤销。", "category.delete.confirm": "删除", "category.delete.cancel": "取消", "category.move.title": "移动分类到其他分类", "category.add.subcategory.title": "添加子分类", "category.add.subcategory.placeholder": "请输入子分类名称", "category.add.subcategory.failed": "添加子分类失败", "category.validation.name.required": "分类名称不能为空", "category.validation.name.too.long": "分类名称不能超过50个字符", "category.renamed": "分类已重命名", "category.deleted": "分类\"{{name}}\"已删除", "category.moved": "分类已移动", "category.subcategory.added": "子分类已添加", "category.settings.saved": "分类设置已保存", "category.settings.save.failed": "保存设置失败", "category.type.change.error": "无法变更分类类型", "category.type.change.error.message": "该分类下存在账户或交易数据，无法安全变更类型", "category.type.change.checking": "正在检查类型变更的安全性...", "category.type.change.safe": "可以安全变更类型", "category.type.change.warning": "类型变更存在风险", "category.type.change.danger": "无法变更类型", "category.type.change.accounts.count": "该分类下有 {{count}} 个账户", "category.type.change.transactions.count": "相关账户有 {{count}} 条交易记录", "category.type.change.balance.records.count": "包含 {{count}} 条余额调整记录", "category.type.change.risk.description": "变更账户类型会导致现有交易数据与新类型不匹配，可能造成数据不一致。建议先清理相关数据后再进行类型变更。", "category.existing.top.level": "现有顶级分类", "category.name.duplicate.warning": "分类名称已存在，请选择其他名称", "category.name.available": "分类名称可用", "category.name.duplicate.error": "分类名称 \"{{name}}\" 已存在，请使用其他名称", "category.create.unknown.error": "创建分类时发生未知错误", "category.get.failed": "获取分类失败", "category.not.found": "分类不存在", "category.parent.not.found": "父分类不存在", "category.top.level.type.required": "顶级分类必须指定账户类型", "category.create.success": "分类创建成功", "category.parent.invalid": "父分类不存在或无效", "category.name.already.exists": "该分类名称已存在", "category.top.level.cannot.move": "顶层分类不允许移动", "category.cannot.set.self.as.parent": "分类不能设置自己为父分类", "category.type.change.not.allowed": "无法变更分类类型：该分类下存在账户或交易数据，变更类型会导致数据不一致", "category.update.success": "分类更新成功", "category.update.failed": "更新分类失败", "category.has.children.cannot.delete": "该分类存在子分类，无法删除", "category.has.accounts.cannot.delete": "该分类存在账户，无法删除", "category.delete.failed": "删除分类失败", "category.cannot.move.to.descendant": "不能将分类移动到其子分类下", "category.cannot.move.different.type": "子分类只能在同类型的分类范围内移动", "category.delete.success": "分类删除成功"}