{"common.save": "保存", "common.saving": "保存中...", "common.cancel": "取消", "common.close": "关闭", "common.delete": "删除", "common.deleting": "删除中...", "common.edit": "编辑", "common.add": "添加", "common.confirm": "确认", "common.loading": "加载中...", "common.redirecting": "正在初始化...", "common.processing": "处理中...", "common.initializing": "正在初始化...", "common.preparing": "正在准备...", "common.loading.data": "正在加载数据...", "common.loading.page": "正在加载页面...", "common.loading.app": "正在启动应用...", "common.search": "搜索", "common.filter": "筛选", "common.export": "导出", "common.import": "导入", "common.refresh": "刷新", "common.back": "返回", "common.next": "下一步", "common.previous": "上一步", "common.submit": "提交", "common.submitting": "提交中...", "common.reset": "重置", "common.clear": "清除", "common.records": "条记录", "common.clear.all": "清除所有筛选", "common.clear.filter": "清除筛选", "common.select": "选择", "common.all": "全部", "common.items": "笔", "common.none": "无", "common.empty": "空", "common.other": "其他", "common.yes": "是", "common.no": "否", "common.ok": "确定", "common.error": "错误", "common.success": "成功", "common.warning": "警告", "common.info": "信息", "error.unknown": "未知错误", "common.switch.to": "切换到", "common.new": "新", "common.delete.failed": "删除失败", "common.actions": "操作", "common.confirm.clear": "确认清空", "common.confirm.delete": "确认删除", "common.total": "共", "common.pagination.previous": "上一页", "common.pagination.next": "下一页", "common.pagination.showing": "显示第 {{start}} 到 {{end}} 条，共 {{total}} 条记录", "common.date.today": "今天", "common.date.yesterday": "昨天", "common.date.days.ago": "{{days}}天前", "common.date.tomorrow": "明天", "common.date.days.later": "{{days}}天后", "common.date.future": "未来", "common.retry": "重试", "common.this.year": "今年", "common.theme.light": "明亮模式", "common.theme.dark": "深色模式", "common.theme.system": "跟随系统", "common.theme.light.short": "明亮", "common.theme.dark.short": "深色", "common.theme.system.short": "自动", "common.theme.select": "选择主题", "common.app.subtitle": "个人财务管理", "common.income": "收入", "common.expense": "支出", "common.currency": "货币", "common.save.changes": "保存更改", "input.dialog.required": "请输入内容", "delete.confirm.message": "确定要删除{{itemType}}「{{itemName}}」吗？", "delete.confirm.warning": "此操作不可撤销，请谨慎操作。", "delete.clear.related.data": "清空相关数据", "delete.clear.and.delete.warning": "将先清空相关数据，然后删除{{itemType}}。此操作不可撤销。", "delete.confirm.clear.and.delete": "确认清空并删除", "delete.confirm": "确认删除", "delete.password.prompt": "请输入密码确认删除：", "delete.password.placeholder": "请输入密码", "common.network.error": "网络错误，请稍后重试", "common.no.data": "暂无数据", "common.more": "更多", "transaction.notes": "备注", "transaction.tags": "标签", "transaction.tags.empty": "暂无标签", "transaction.tag.recurring": "定期", "transaction.tag.loan": "贷款", "transaction.tag.loan.payment": "还款", "transaction.tag.loan.principal": "本金", "tag.create.new": "创建新标签", "recurring.transaction.delete": "删除定期交易", "confirm.delete.recurring.transaction.message": "确定要删除这个定期交易吗？此操作无法撤销。", "loan.contracts": "贷款合约", "loan.contract.create": "创建贷款合约", "loan.contract.add.new": "点击添加新的贷款合约", "loan.contract.limit.title": "贷款合约限制", "exchange.rate.auto.generated.reverse": "自动生成的反向汇率，基于 {fromCurrency}→{toCurrency}", "exchange.rate.auto.generated.transitive": "自动生成的传递汇率，计算路径: {calculationPath}", "exchange.rate.create.success": "汇率创建成功", "exchange.rate.update.success": "汇率更新成功", "exchange.rate.auto.generate.failed": "自动重新生成汇率失败", "exchange.rate.auto.generate.partial.failed": "自动生成汇率部分失败: {errors}", "exchange.rate.auto.generate.success": "成功自动生成 {count} 条汇率记录", "exchange.rate.invalid.date.format": "无效的日期格式", "exchange.rate.auto.generate.process.failed": "自动生成汇率失败", "exchange.rate.transitive.generate.failed": "生成传递汇率失败: {error}", "exchange.rate.transitive.process.failed": "传递汇率生成过程失败: {error}", "exchange.rate.cleanup.failed": "清理自动生成汇率失败: {error}", "loan.contract.validation.failed": "贷款参数验证失败: {errors}", "loan.contract.payment.day.invalid": "还款日期必须在1-31号之间", "loan.contract.currency.not.found": "指定的货币不存在", "loan.contract.account.invalid": "无效账户：必须是负债账户", "loan.contract.account.cannot.change.with.payments": "存在已完成的还款记录时不能更改账户", "loan.contract.currency.cannot.change.with.payments": "存在已完成的还款记录时不能更改货币", "loan.contract.not.found": "贷款合约不存在", "loan.contract.generation.failed": "贷款合约 {contractId} 生成失败: {error}", "balance.change.amount.pattern": "变化金额：{amount}", "loan.contract.template.default.description": "{contractName} - 第{period}期{type}", "loan.contract.template.default.notes": "贷款合约: {contractName}", "loan.contract.template.balance.adjustment": "账户余额调整 - {contractName}第{period}期本金还款", "loan.contract.template.balance.notes": "贷款合约: {contractName}，剩余本金: {remainingBalance}", "loan.contract.limit.message": "一个账户最多只能绑定一个贷款合约", "loan.contract.updated": "贷款合约已更新", "loan.contract.created": "贷款合约已创建", "loan.contract.save.failed": "保存贷款合约失败", "loan.contract.create.failed": "创建贷款合约失败", "loan.contract.update.failed": "更新贷款合约失败", "loan.contract.delete.failed": "删除贷款合约失败", "loan.contract.fetch.failed": "获取贷款合约列表失败", "loan.contract.unauthorized": "未授权访问", "loan.contract.missing.fields": "缺少必要字段", "loan.contract.amount.invalid": "贷款金额必须大于0", "loan.contract.rate.invalid": "利率必须在0-100%之间", "loan.contract.periods.invalid": "总期数必须是正整数", "loan.contract.payment.day.range.invalid": "还款日期必须在1-31号之间", "loan.contract.account.has.existing": "该账户已有贷款合约，一个账户最多只能绑定一个贷款合约", "loan.contract.not.exists": "贷款合约不存在", "loan.contract.periods.too.small": "新的总期数必须大于已完成的最大期数 ({maxPeriod})", "loan.contract.deleted": "贷款合约已删除", "loan.contract.payment.account.invalid": "还款账户不存在、不是支出账户或货币不匹配", "loan.contract.no.payments.to.reset": "没有找到可重置的还款记录", "loan.contract.no.completed.payments": "没有已完成的还款记录可重置", "loan.type.principal": "本金", "loan.type.interest": "利息", "loan.type.balance.update": "余额更新", "loan.validation.rate.too.high": "利率超过30%，请确认是否正确", "loan.validation.rate.too.low": "利率低于1%，请确认是否为优惠利率", "loan.validation.periods.too.long": "贷款期数超过30年，请确认是否合理", "loan.validation.periods.too.short": "短期贷款建议考虑其他融资方式", "loan.validation.amount.too.large": "贷款金额较大，请确认风险承受能力", "loan.validation.start.date.too.old": "贷款开始日期距今超过30天，请确认是否为历史贷款", "loan.validation.payment.day.month.end": "还款日设置在月末可能导致某些月份无法正常还款", "loan.validation.payment.day.suggestion": "建议将还款日设置在1-28号之间", "loan.validation.interest.only.too.long": "只还利息的贷款期数较长，请确认最终还本计划", "loan.validation.data.format.error": "请检查输入数据格式是否正确", "loan.validation.unknown.error": "验证过程中发生未知错误", "loan.validation.account.not.found": "指定的贷款账户不存在", "loan.validation.account.not.liability": "贷款账户必须是负债类型", "loan.validation.payment.account.not.found": "指定的还款账户不存在", "loan.validation.payment.account.not.expense": "还款账户必须是支出类型", "loan.validation.currency.mismatch": "贷款账户和还款账户的货币必须一致", "loan.validation.payment.account.suggestion": "建议设置还款账户以便自动生成还款交易", "loan.validation.account.association.error": "验证账户关联时发生错误", "loan.validation.duplicate.contract": "该账户已存在活跃的贷款合约", "loan.validation.duplicate.check.error": "无法检查重复合约，请手动确认", "loan.payment.reset.unauthorized": "未授权访问", "loan.payment.reset.select.records": "请选择要重置的还款记录", "loan.payment.reset.failed": "重置还款记录失败", "loan.contracts.empty.title": "暂无贷款合约", "loan.contracts.empty.description": "点击添加第一个贷款合约", "loan.interest.rate": "利率", "loan.repayment.type": "还款方式", "loan.monthly.payment": "月还款额", "loan.next.payment": "下次还款", "loan.progress": "还款进度", "loan.periods": "期", "loan.status.active": "活跃", "loan.status.inactive": "暂停", "loan.repayment.equal.payment": "等额本息", "loan.repayment.equal.principal": "等额本金", "loan.repayment.interest.only": "只还利息", "loan.amount": "贷款金额", "loan.total.periods": "总期数", "loan.payment.settings": "还款设置", "loan.payment.day": "每月还款日", "loan.payment.day.description": "每月几号还款（1-31号）", "loan.payment.account": "还款账户", "loan.payment.account.select": "请选择还款账户", "loan.payment.account.description": "选择用于自动扣款的支出账户，货币必须与贷款账户一致", "loan.transaction.template": "交易模板", "loan.category.settings": "分类设置", "loan.principal.category": "本金还款分类", "loan.interest.category": "利息支出分类", "category.select": "请选择分类", "loan.payment.day.monthly": "每月{day}号", "loan.start.date": "开始日期", "loan.payment.account.no.available": "无可用的还款账户", "tags.no.available": "无可用标签", "category.no.available": "无可用分类", "mortgage.loan.create": "创建房贷合约", "mortgage.loan.create.description": "为房屋贷款创建合约", "loan.contract.create.description": "创建普通贷款合约", "mortgage.loan.edit": "编辑房贷合约", "mortgage.loan.name": "房贷合约名称", "mortgage.loan.amount": "贷款金额", "mortgage.loan.interest.rate": "年利率", "mortgage.loan.total.periods": "贷款期限", "mortgage.loan.periods.10years": "10年 (120期)", "mortgage.loan.periods.15years": "15年 (180期)", "mortgage.loan.periods.20years": "20年 (240期)", "mortgage.loan.periods.25years": "25年 (300期)", "mortgage.loan.periods.30years": "30年 (360期)", "mortgage.loan.repayment.type": "还款方式", "mortgage.loan.repayment.equal.payment": "等额本息", "mortgage.loan.repayment.equal.principal": "等额本金", "mortgage.loan.payment.settings": "还款设置", "mortgage.loan.start.date": "开始日期", "mortgage.loan.payment.day": "每月还款日", "mortgage.loan.payment.day.description": "每月的还款日期（1-31号）", "mortgage.loan.payment.account": "还款账户", "mortgage.loan.payment.account.select": "请选择还款账户", "mortgage.loan.payment.account.description": "选择用于还款的支出账户（可选）", "mortgage.loan.payment.account.no.available": "无可用的还款账户", "mortgage.loan.transaction.template": "交易模板", "mortgage.loan.cancel": "取消", "mortgage.loan.save": "保存", "loan.payment.history": "还款记录", "loan.payment.history.view": "查看还款记录", "loan.payment.history.empty.title": "暂无还款记录", "loan.payment.history.empty.description": "还没有任何还款记录", "loan.payment.period": "期数", "loan.payment.period.number": "第{period}期", "loan.payment.date": "还款日期", "loan.payment.principal": "本金", "loan.payment.interest": "利息", "loan.payment.total": "总金额", "loan.payment.remaining.balance": "剩余余额", "loan.payment.summary": "还款统计", "loan.payment.total.periods": "已还期数", "loan.payment.total.paid": "累计还款", "loan.payment.current.balance": "当前余额", "loan.contract.create.title": "创建贷款合约", "loan.payment.status.completed": "已完成", "loan.payment.status.pending": "待处理", "loan.payment.status.failed": "失败", "transaction.quick.tag.create.success": "标签创建成功", "transaction.quick.tag.added.success": "新标签已添加到选中列表", "preferences.data.generation.settings": "数据生成设置", "preferences.data.generation.description": "配置定期交易和贷款还款的未来数据生成", "preferences.future.data.days": "提前生成天数", "preferences.future.data.days.help": "系统将生成指定天数内的定期交易和贷款还款记录。设置0天表示不生成未来记录，只生成截止到当天的记录；设置1天将生成明天的记录；设置7天将生成未来7天的记录", "preferences.future.data.days.disabled": "不生成未来记录", "common.days": "天", "common.server.error": "服务器内部错误"}