{"exchange.rate.alert.title": "需要设置汇率", "exchange.rate.alert.description": "您有 {{count}} 个货币对需要设置汇率，以确保统计数据的准确性：", "exchange.rate.alert.more.pairs": "还有 {{count}} 个货币对...", "exchange.rate.alert.setup.now": "立即设置汇率", "exchange.rate.alert.ignore": "暂时忽略", "exchange.rate.alert.close": "关闭", "exchange.rate.fetch.failed": "获取缺失汇率失败", "exchange.rate.management": "汇率管理", "exchange.rate.management.description": "管理您的货币汇率设置，确保所有统计数据能正确转换为本位币", "exchange.rate.loading": "正在加载汇率数据...", "exchange.rate.base.currency": "本位币", "exchange.rate.missing.title": "需要设置汇率", "exchange.rate.missing.description": "您有 {{count}} 个货币对需要设置汇率，以便正确计算统计数据。", "exchange.rate.setup": "设置汇率", "exchange.rate.count": "已设置 {{count}} 个汇率", "exchange.rate.add": "添加汇率", "exchange.rate.edit": "编辑汇率", "exchange.rate.update": "更新汇率", "exchange.rate.form.incomplete": "请填写所有必填字段", "exchange.rate.invalid.rate": "汇率必须是大于0的数字", "exchange.rate.same.currency": "源货币和目标货币不能相同", "exchange.rate.from.currency": "源货币", "exchange.rate.to.currency": "目标货币", "exchange.rate.from.currency.help": "要转换的原始货币", "exchange.rate.to.currency.help": "转换后的目标货币", "exchange.rate.rate": "汇率", "exchange.rate.rate.placeholder": "例如: 7.2", "exchange.rate.rate.help": "1单位源货币 = ? 单位目标货币", "exchange.rate.effective.date": "生效日期", "exchange.rate.effective.date.help": "汇率开始生效的日期", "exchange.rate.notes": "备注", "exchange.rate.notes.placeholder": "可选的备注信息...", "exchange.rate.deleted": "汇率已删除", "exchange.rate.empty.title": "暂无汇率设置", "exchange.rate.empty.description": "点击上方\"添加汇率\"按钮开始设置汇率", "exchange.rate.list": "汇率列表", "exchange.rate.currency.pair": "货币对", "exchange.rate.delete.confirm.title": "确认删除", "exchange.rate.delete.confirm": "确认删除", "exchange.rate.delete.confirm.message": "您确定要删除这个汇率设置吗？此操作无法撤销。", "exchange.rate.auto.update": "自动更新汇率", "exchange.rate.auto.update.description": "启用后，系统将自动从 Frankfurter API 获取最新汇率", "exchange.rate.manual.update": "手动更新", "exchange.rate.manual.update.description": "立即从 Frankfurter API 获取最新汇率", "exchange.rate.last.update": "最后更新", "exchange.rate.never.updated": "从未更新", "exchange.rate.updating": "正在更新汇率...", "exchange.rate.update.success": "汇率更新成功", "exchange.rate.update.failed": "汇率更新失败", "exchange.rate.update.partial": "部分汇率更新成功", "exchange.rate.auto.update.settings": "汇率自动更新设置", "exchange.rate.source.frankfurter": "数据来源：Frankfurter API", "exchange.rate.base.currency.required": "请先设置本位币", "exchange.rate.no.currencies": "没有找到可用的货币", "exchange.rate.input.rates": "输入汇率", "exchange.rate.auto.generated.rates": "自动生成汇率", "exchange.rate.type.user": "手动输入", "exchange.rate.type.api": "API更新", "exchange.rate.type.auto": "自动生成", "exchange.rate.type": "类型", "exchange.rate.no.input.rates": "暂无输入的汇率", "exchange.rate.no.auto.rates": "暂无自动生成的汇率", "exchange.rate.invalid.currency": "无效的货币", "exchange.rate.created": "汇率创建成功", "exchange.rate.updated": "汇率更新成功", "exchange.rate.create.success": "汇率创建成功", "exchange.rate.create.failed": "创建汇率失败", "exchange.rate.auto.update.enabled": "已启用汇率自动更新", "exchange.rate.auto.update.disabled": "已禁用汇率自动更新", "exchange.rate.base.currency.setup.required": "请先在偏好设置中设置本位币", "exchange.rate.update.partial.message": "成功更新 {{updatedCount}} 个汇率，{{errorCount}} 个失败", "exchange.rate.update.success.message": "成功更新 {{updatedCount}} 个汇率", "exchange.rate.update.skipped.message": "，跳过 {{skippedCount}} 个不支持的货币", "exchange.rate.update.general.failed": "更新失败", "exchange.rate.network.error": "网络错误，请稍后重试", "exchange.rate.settings.update.failed": "更新设置失败", "exchange.rate.api.currency.not.supported": "本位币 {{currencyCode}} 不支持自动汇率更新，请检查货币代码是否正确或手动输入汇率", "exchange.rate.api.service.unavailable": "汇率服务暂时不可用，请稍后重试", "exchange.rate.api.error.with.code": "获取汇率数据失败（错误代码：{{statusCode}}），请稍后重试", "exchange.rate.network.connection.failed": "网络连接失败，请检查网络连接后重试", "exchange.rate.api.fetch.failed": "获取汇率数据失败，请稍后重试"}