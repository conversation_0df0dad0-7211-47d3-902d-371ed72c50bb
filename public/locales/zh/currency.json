{"currency.currency": "货币", "currency.label": "货币", "currency.all": "全部货币", "currency.management": "货币管理", "currency.management.description": "管理您可以使用的货币。只有添加到可用列表的货币才能在账户、交易和汇率设置中使用。", "currency.selected": "已选择的货币", "currency.available": "可添加的货币", "currency.none.selected": "还没有选择任何货币", "currency.add.instruction": "请从下方添加您需要使用的货币", "currency.custom": "自定义", "currency.custom.create": "创建自定义货币", "currency.custom.create.success": "自定义货币创建成功", "currency.custom.edit": "编辑自定义货币", "currency.custom.update.failed": "更新自定义货币失败", "currency.code": "货币代码", "currency.name": "货币名称", "currency.symbol": "货币符号", "currency.decimalPlaces": "小数位数", "currency.decimalPlaces.unit": "位", "currency.decimalPlaces.help": "设置该货币显示的小数位数（0-10位）", "currency.code.placeholder": "如: BTC, USDT", "currency.name.placeholder": "如: <PERSON><PERSON><PERSON>, <PERSON><PERSON>", "currency.symbol.placeholder": "如: ₿, ₮", "currency.code.help": "3-10个大写字母或数字", "currency.add": "添加", "currency.remove": "移除", "currency.add.failed": "添加货币失败", "currency.remove.failed": "删除货币失败", "currency.update.failed": "更新货币设置失败", "currency.form.incomplete": "请填写完整的货币信息", "currency.custom.create.failed": "创建自定义货币失败", "currency.custom.delete.failed": "删除自定义货币失败", "currency.important.tips": "重要提示", "currency.tip.custom.create": "您可以创建自定义货币（如加密货币、积分等）", "currency.tip.code.format": "自定义货币代码必须是3-10个大写字母或数字的组合", "currency.tip.decimal.places": "小数位数决定了该货币金额显示的精度，建议根据实际使用场景设置", "currency.tip.delete.warning": "删除货币前，请确保没有相关的交易记录和汇率设置", "currency.tip.base.currency": "本位币不能被删除，如需更换请先在偏好设置中修改本位币", "currency.tip.major.currency": "建议至少保留一种主要货币（如 USD、EUR、CNY）", "currency.setup.required": "需要设置本位币", "currency.setup.description": "请先设置您的本位币，以便正确显示金额和进行货币转换。", "currency.setup.action": "前往设置", "currency.setup.inline": "请先设置本位币", "currency.converter.title": "货币转换器", "currency.converter.amount": "金额", "currency.converter.no.rates": "暂无可用汇率", "currency.converter.reverse.note": "基于反向汇率计算", "currency.add.success": "货币添加成功", "currency.remove.success": "货币移除成功", "currency.custom.delete.success": "自定义货币删除成功", "currency.invalid.currency": "无效的货币", "currency.decimal.places.invalid": "小数位数必须在 0-10 之间", "currency.not.found": "指定的货币不存在", "currency.permission.denied": "您没有权限使用此货币，请先在货币管理中添加", "currency.get.failed": "获取货币列表失败", "currency.custom.fields.required": "货币代码、名称和符号都不能为空", "currency.custom.decimal.places.invalid": "小数位数必须是0-10之间的整数", "currency.custom.code.format.invalid": "货币代码必须是3-10个大写字母或数字", "currency.custom.code.already.exists": "您已创建过该货币代码", "currency.custom.code.already.selected": "您已选择了货币代码为 {{code}} 的其他货币，同一货币代码只能选择一次", "currency.custom.get.failed": "获取自定义货币失败", "currency.custom.cannot.delete.base": "不能删除本位币，请先更改本位币设置", "currency.custom.has.transactions": "该货币有 {{count}} 条交易记录，不能删除", "currency.custom.has.exchange.rates": "该货币有 {{count}} 条汇率设置，不能删除", "currency.custom.name.symbol.required": "名称和符号都不能为空", "currency.custom.update.success": "自定义货币更新成功"}